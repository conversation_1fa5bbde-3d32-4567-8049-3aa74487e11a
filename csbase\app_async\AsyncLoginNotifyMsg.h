#ifndef _ASYNC_LOGIN_NOTIFY_MSG_H_
#define _ASYNC_LOGIN_NOTIFY_MSG_H_

#include "string.h"
#include "DclientMsgSt.h"
#include "AK.Server.pb.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "AsyncNotifyMsg.h"

class AsyncLoginNotifyMsg: public AsyncNotifyMsg
{
public:
    AsyncLoginNotifyMsg() {}
    ~AsyncLoginNotifyMsg() = default;
    std::string BuildAsyncMsg();
    void SetOldVersion(int old_version) { old_version_ = old_version; } 
    const std::string& GetMsgType() override { return msg_type_; }
private:
    int old_version_ = 0;
    std::string msg_type_ = "login_notify";
};

#endif // _ASYNC_LOGIN_NOTIFY_MSG_H_
