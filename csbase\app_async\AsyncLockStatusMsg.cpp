#include "AsyncLockStatusMsg.h"
#include "json/json.h"
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "backend/MsgBuild.h"
#include "util.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"

std::string AsyncLockStatusMsg::BuildAsyncMsg()
{
    Json::Value json_datas;
    json_datas["lock_uuid"] = lock_uuid_;
    json_datas["status"] = status_;
    
    return BaseBuildAsyncMsg(json_datas);
}

int AsyncLockStatusMsg::BuildRouteP2PMsg(uint64_t trace_id, AK::Server::P2PAppAsyncMsg& p2p_msg)
{
    p2p_msg.set_trace_id(trace_id);
    p2p_msg.set_msg_type(GetMsgType());
    p2p_msg.set_json(BuildAsyncMsg());
    
    return 0;
}
