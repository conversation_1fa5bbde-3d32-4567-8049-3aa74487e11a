#ifndef __DB_NOTIFY_LIST_H__
#define __DB_NOTIFY_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ResidentDevices.h"


namespace dbinterface {

class PushButtonNotifyList
{
public:
    static void InsertPushButtonNotifyList(const ResidentDev& dev, const std::string& notify_uuid);
    static int PushButtonNotifyInsOrPm(const std::string& receiver_uuid, const std::string& notify_uuid, const std::string& project_uuid);

private:
    PushButtonNotifyList() = delete;
    ~PushButtonNotifyList() = delete;
};

}
#endif