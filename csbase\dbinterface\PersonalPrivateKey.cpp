#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/DataConfusion.h"
#include "Rldb/ConnectionManager.h"
        
namespace dbinterface
{

PersonalPrivateKey::PersonalPrivateKey()
{

}

int PersonalPrivateKey::GetPersonalPrivateKeyByID(int id, PersonalPrivateKeyInfo &key_info)
{
    std::stringstream streamSQL;
    streamSQL << "select MngAccountID,UnitID,Node,Code,AccountID from PersonalPrivateKey where ID = " << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {

        key_info.id = id;
        key_info.mng_id = ATOI(query.GetRowData(0));
        key_info.unit_id = ATOI(query.GetRowData(1));
        Snprintf(key_info.node, sizeof(key_info.node), query.GetRowData(2));
        Snprintf(key_info.code, sizeof(key_info.code), query.GetRowData(3));
        key_info.account_id = ATOI(query.GetRowData(4));
    }

    ReleaseDBConn(conn);
    return 0;    
}

//通过家庭跟code，获取对应账号和uuid
int PersonalPrivateKey::GetPersonalPrivateKeyByCode(const std::string& node, const std::string& code,
                                                            PersonalPrivateKeyInfo &key_info)
{
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select A.Name, A.UUID from PersonalAccount A \
                   left join  PersonalPrivateKey R on A.ID=R.AccountID \
                   WHERE R.Node='%s' AND R.Code = '%s' limit 1",
               node.c_str(), code.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        Snprintf(key_info.account_name, sizeof(key_info.account_name), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(key_info.account_uuid, sizeof(key_info.account_uuid), query.GetRowData(1));
    }
    ReleaseDBConn(conn);
    return 0;

}

//通过code查找持卡人的昵称
int PersonalPrivateKey::GetNameAndNodeFromPriKeyForCommunityPubPersonal(int grade, const std::string& code, int unit_id, int mng_id, std::vector<PersonalPrivateKeyInfo>& pri_infos)
{
    std::string name;
    char sql[1024] = {0};


    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        ::snprintf(sql, 1024, "select A.Name,R.Node, A.RoomID from PersonalAccount A left join  PersonalPrivateKey R on A.ID=R.AccountID \
        WHERE  R.Code='%s' and R.MngAccountID='%d' and R.UnitID='%d' ", code.c_str(), mng_id, unit_id);
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        ::snprintf(sql, 1024, "select A.Name,R.Node, A.RoomID from PersonalAccount A left join  PersonalPrivateKey R on A.ID=R.AccountID \
        WHERE  R.Code='%s' and R.MngAccountID='%d' ", code.c_str(), mng_id);
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    while (query.MoveToNextRow())
    {
        PersonalPrivateKeyInfo pri_info;
        memset(&pri_info, 0, sizeof(pri_info));

        ::snprintf(pri_info.account_name, sizeof(pri_info.account_name), "%s", dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        ::snprintf(pri_info.node, sizeof(pri_info.node), "%s", query.GetRowData(1));
        pri_info.room_id = ATOI(query.GetRowData(2));

        pri_infos.push_back(pri_info);
    }
    ReleaseDBConn(conn);
    return 0;

}


std::string PersonalPrivateKey::GetNameFromPriKeyForCommunityPubWork(const std::string& code, int mng_id, const std::string& mac)
{
    std::string name;
    char sql[1024] = {0};
    //社区下pin是唯一的
    ::snprintf(sql, 1024, "select P.Name from PubPrivateKey  P left join PubPrivateKeyList  L on L.KeyID=P.ID \
    where L.mac='%s' and P.MngAccountID='%d' and P.Code='%s';", mac.c_str(), mng_id, code.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return name;

}

PRIVATE_KEY* PersonalPrivateKey::GetRootBothPrivateKeyList(const std::string& user)
{
    const int default_realy = 15;
    PRIVATE_KEY* private_key_header = NULL;
    PRIVATE_KEY* cur_privatekey = NULL;

    //遍历数据库查询所有节点信息
    std::stringstream streamSQL;
    std::string address;
    std::string tmp_address;
    streamSQL << "select K.ID,K.Type,K.MngAccountID,K.UnitID,K.Code,K.Status,K.Node,K.CreateTime,K.ExpireTime,K.Access,K.AccountID,\
                P.Name,P.RoomID,P.Role,L.Relay,L.SecurityRelay,L.MAC,K.IsOpenAllDoor  \
                from PersonalPrivateKey K \
                inner join PersonalAccount P on K.AccountID=P.ID  \
                left  join PersonalPrivateKeyList L on K.ID=L.KeyID  \
                where K.Node ='" << user << "' order by ID DESC";

    GET_DB_CONN_ERR_RETURN(conn, nullptr)

    CRldbQuery query(conn.get());

    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        int role = ATOI(query.GetRowData(13));
        if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            break;//区分是个人还是社区,社区不走这个流程了
        }

        int is_open_all_door = ATOI(query.GetRowData(17));

        PRIVATE_KEY* newkey = new PRIVATE_KEY;
        memset(newkey, 0, sizeof(PRIVATE_KEY));
        newkey->id = ATOI(query.GetRowData(0));
        newkey->type = ATOI(query.GetRowData(1));
        newkey->mng_account_id = ATOI(query.GetRowData(2));
        newkey->unit_id = ATOI(query.GetRowData(3));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(4));
        newkey->status = ATOI(query.GetRowData(5));
        Snprintf(newkey->node, sizeof(newkey->node), query.GetRowData(6));
        Snprintf(newkey->create_time, sizeof(newkey->create_time), query.GetRowData(7));
        Snprintf(newkey->expire_time, sizeof(newkey->expire_time), query.GetRowData(8));
        Snprintf(newkey->access, sizeof(newkey->access), query.GetRowData(9));
        newkey->account_id = ATOI(query.GetRowData(10));
        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(11)).c_str());
        newkey->room_id = ATOI(query.GetRowData(12));
        newkey->relay = (strlen(query.GetRowData(14)) == 0 && is_open_all_door == 1) ? default_realy : ATOI(query.GetRowData(14));
        newkey->security_relay = (strlen(query.GetRowData(15)) == 0 && is_open_all_door == 1) ? default_realy : ATOI(query.GetRowData(15));
        Snprintf(newkey->access_mac, sizeof(newkey->access_mac), query.GetRowData(16));

        if (private_key_header == NULL)
        {
            private_key_header = newkey;
        }
        else
        {
            cur_privatekey->next = newkey;
        }

        cur_privatekey = newkey;
    }
    return private_key_header;
}

void PersonalPrivateKey::GetCommunityMacPrivList(DEVICE_SETTING* dev_setting, PRIVATE_KEY** keylist)
{
    PRIVATE_KEY* rf_key_header = *keylist;
    if (rf_key_header)
    {
        while (rf_key_header->next)
        {
            rf_key_header = rf_key_header->next;

        }
    }
    PRIVATE_KEY* cur_rf_key = rf_key_header;

    std::stringstream streamSQL;
    streamSQL << "select P.Code,P.Name,P.OwnerType,P.SchedulerType,P.DateFlag,P.BeginTime,P.EndTime,P.StartTime,P.StopTime,L.Relay,L.SecurityRelay from PubPrivateKey P \
        left join PubPrivateKeyList  L on L.KeyID=P.ID  where L.Mac='" << dev_setting->mac << "' and P.MngAccountID=" << dev_setting->manager_account_id;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        PRIVATE_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(0));
        Snprintf(newkey->user, sizeof(newkey->user), query.GetRowData(1));
        int role = ATOI(query.GetRowData(2));
        if (role == 0) //物业
        {
            newkey->type = 1;
        }
        else if (role == 1)//快递
        {
            newkey->type = 2;
        }
        if (strlen(newkey->code) == 0)
        {
            delete newkey;
            continue;
        }

        if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)
        {
            newkey->relay = 7;
        }
        else
        {
            newkey->relay = ATOI(query.GetRowData(9));
        }

        if (dev_setting->dclient_version >= D_CLIENT_VERSION_6400)
        {
            newkey->security_relay = ATOI(query.GetRowData(10));
        }
        else
        {
            newkey->security_relay = 0;
        }

        int sche_type = ATOI(query.GetRowData(3));
        if (SchedType::ONCE_SCHED == sche_type) //单次计划
        {
            if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)   //旧设备无单次计划,故写死
            {
                Snprintf(newkey->time_start, sizeof(newkey->time_start), "00:00");
                Snprintf(newkey->time_end, sizeof(newkey->time_end), "23:59");
                newkey->week_day = 127;    //即二进制1111111,每天
            }
            else
            {
                Snprintf(newkey->day_start, sizeof(newkey->day_start), query.GetRowData(5));
                Snprintf(newkey->day_end, sizeof(newkey->day_end), query.GetRowData(6));
            }
        }
        else if (SchedType::DAILY_SCHED == sche_type) //每日计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(7));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(8));
            newkey->week_day = 127;    //每天
        }
        else //周计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(7));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(8));
            newkey->week_day = ATOI(query.GetRowData(4));   //周几
        }

        if (rf_key_header == NULL)
        {
            rf_key_header = newkey;
            *keylist = rf_key_header;
        }
        else
        {
            cur_rf_key->next = newkey;
        }

        cur_rf_key = newkey;
    }
    ReleaseDBConn(conn);

}

void PersonalPrivateKey::GetCommunityPerPrivateKey(int allow_app_create_pin, DEVICE_SETTING* dev_setting, PRIVATE_KEY** keylist)
{
    PRIVATE_KEY* key_header = *keylist;
    if (key_header)
    {
        while (key_header->next)
        {
            key_header = key_header->next;

        }
    }
    PRIVATE_KEY* cur_key = key_header;

    std::stringstream sql;
    sql << "select P.Code,P.SchedulerType,P.DateFlag,P.BeginTime,P.EndTime,P.StartTime,P.StopTime,L.Relay,A.Name,C.UnitID,C.RoomName,I.AptPinType,P.Special, L.SecurityRelay \
            from PersonalPrivateKey P left join PersonalPrivateKeyList L on L.KeyID=P.ID left join \
            PersonalAccount A on A.ID=P.AccountID left join PersonalAccount A1 on A1.Account=P.Node \
            left join CommunityRoom C on C.ID = A1.RoomID \
            left join CommunityInfo I on I.AccountID = P.MngAccountID"
        << " where L.Mac= '"
        << dev_setting->mac
        << "' and P.MngAccountID = "
        << dev_setting->manager_account_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        if (!allow_app_create_pin && ATOI(query.GetRowData(12)))
        {
            continue;
        }
    
        PRIVATE_KEY* newkey = new PRIVATE_KEY;
        memset(newkey, 0, sizeof(PRIVATE_KEY));

        if (CommunityInfo::AptPinTypeEnum::APT_PIN == ATOI(query.GetRowData(11)))
        {
            ::snprintf(newkey->code, sizeof(newkey->code), "%s+%s", query.GetRowData(10), query.GetRowData(0));
        }
        else
        {
            Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(0));
        }

        if (strlen(newkey->code) == 0)
        {
            delete newkey;
            continue;
        }

        if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)
        {
            newkey->relay = 7;
        }
        else
        {
            newkey->relay = ATOI(query.GetRowData(7));
        }

        if (dev_setting->dclient_version >= D_CLIENT_VERSION_6400)
        {
            newkey->security_relay = ATOI(query.GetRowData(13));
        }
        else
        {
            newkey->security_relay = 0;
        }

        int sche_type = ATOI(query.GetRowData(1));
        if (SchedType::ONCE_SCHED == sche_type) //单次计划
        {
            if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)   //旧设备无单次计划,故写死
            {
                Snprintf(newkey->time_start, sizeof(newkey->time_start), "00:00");
                Snprintf(newkey->time_end, sizeof(newkey->time_end), "23:59");
                newkey->week_day = 127;    //即二进制1111111,每天
            }
            else
            {
                Snprintf(newkey->day_start, sizeof(newkey->day_start), query.GetRowData(3));
                Snprintf(newkey->day_end, sizeof(newkey->day_end), query.GetRowData(4));
            }
        }
        else if (SchedType::DAILY_SCHED == sche_type) //每日计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(5));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(6));
            newkey->week_day = 127;    //每天
        }
        else //周计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(5));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(6));
            newkey->week_day = ATOI(query.GetRowData(2));   //周几
        }

        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(8)).c_str());
        newkey->building = ATOI(query.GetRowData(9));
        Snprintf(newkey->apt, sizeof(newkey->apt), query.GetRowData(10));
    
        if (key_header == NULL)
        {
            key_header = newkey;
            *keylist = key_header;
        }
        else
        {
            cur_key->next = newkey;
        }

        cur_key = newkey;
    }
    ReleaseDBConn(conn);
}

void PersonalPrivateKey::GetOrderedSingleUserNodePrivateKey(const std::string& node, UserPinInfoList& pin_list)
{
    std::stringstream sql;
    sql << "select K.Code, P.Account from PersonalPrivateKey K left join PersonalAccount P on K.AccountID = P.ID where K.Node = '" << node << "' order by K.ID";

    GET_DB_CONN_ERR_RETURN_VOID(conn)
    CRldbQuery query(conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        UserPinInfo pin_info;
        Snprintf(pin_info.pin, sizeof(pin_info.pin), query.GetRowData(0));
        Snprintf(pin_info.account, sizeof(pin_info.account), query.GetRowData(1));
        pin_list.push_back(pin_info);
    }

    return;
}

void PersonalPrivateKey::GetSmartLockOrderedSingleUserNodePrivateKey(const std::string& node, UserPinInfoList& pin_list)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn)

    CRldbQuery query(db_conn.get());
    std::stringstream sql;
    sql << " select K.Code,A.Account,P.CredentialID,P.Status,K.UUID,K.IsOpenAllSmartLock,P.SmartLockUUID,A.UUID From PersonalPrivateKey K"
        << " left join SmartLockPrivateKey P on K.UUID = P.PersonalPrivateKeyUUID"
        << " left join PersonalAccount A on K.AccountID = A.ID"
        << " where K.Node = '"<< node << "' order by K.ID";

    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        UserPinInfo pin_info;
        Snprintf(pin_info.pin, sizeof(pin_info.pin), query.GetRowData(0));
        Snprintf(pin_info.account, sizeof(pin_info.account), query.GetRowData(1));
        const char* credential_id = query.GetRowData(2);
        pin_info.credential_id = ATOI(credential_id);
        if (strlen(credential_id) == 0)
        {
            pin_info.credential_id = -1;
        }
        
        pin_info.state = static_cast<SmartLockCredentialState>(ATOI(query.GetRowData(3)));
        Snprintf(pin_info.pin_uuid, sizeof(pin_info.pin_uuid), query.GetRowData(4));
        pin_info.is_support_all_smartlock = ATOI(query.GetRowData(5)) == 1;
        Snprintf(pin_info.smartlock_uuid, sizeof(pin_info.smartlock_uuid), query.GetRowData(6));
        Snprintf(pin_info.personal_account_uuid, sizeof(pin_info.personal_account_uuid), query.GetRowData(7));
        pin_info.project_type = project::PROJECT_TYPE::PERSONAL;
        pin_list.push_back(pin_info);
    }
}

int PersonalPrivateKey::GetPersonalPrivateKeyByUUID(const std::string& uuid, PersonalPrivateKeyInfo &key_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    std::stringstream streamSQL;
    streamSQL << "select MngAccountID,UnitID,Node,Code,AccountID from PersonalPrivateKey where UUID = '" << uuid << "';";

    CRldbQuery query(db_conn.get());
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {
        key_info.mng_id = ATOI(query.GetRowData(0));
        key_info.unit_id = ATOI(query.GetRowData(1));
        Snprintf(key_info.node, sizeof(key_info.node), query.GetRowData(2));
        Snprintf(key_info.code, sizeof(key_info.code), query.GetRowData(3));
        key_info.account_id = ATOI(query.GetRowData(4));

        return 0;
    }

    return -1;    
}

}

