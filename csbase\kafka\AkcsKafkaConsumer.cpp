#include <stdexcept>
#include <iostream>
#include <csignal>
#include <unistd.h>
#include <signal.h>
#include <sstream> 
#include "AkLogging.h"  
#include "AkcsMonitor.h"
#include "AkcsKafkaConsumer.h"

// 静态成员变量定义
thread_local uint64_t AkcsKafkaConsumer::comsumer_batch_num_ = 1;

uint64_t GetUnReadeNum(const std::string &topic, const cppkafka::Consumer &consumer, const cppkafka::Message &msg)
{
    int64_t committed_offset = msg.get_offset() + 1; // 消费的偏移量是当前消息的偏移量 + 1
    cppkafka::TopicPartition partition(topic, msg.get_partition());
    
    auto offsets = consumer.get_offsets(partition);
//    int64_t low_offset = std::get<0>(offsets);
    int64_t high_offset = std::get<1>(offsets);
    int64_t unread_messages = high_offset - committed_offset;

    return unread_messages;
}

std::string FormatPartitions(const cppkafka::TopicPartitionList& partitions) 
{
    std::ostringstream oss;
    for (const auto& partition : partitions) 
    {
        oss << partition.get_topic() << " [Partition: " << partition.get_partition() << ", Offset: " << partition.get_offset() << "]" << std::endl;
    }
    
    return oss.str();
}

AkcsKafkaConsumer::AkcsKafkaConsumer()
{
    status_ = false;
}


void AkcsKafkaConsumer::StartInner()
{
    AK_LOG_INFO << "KafkaConsumerInit kafka_broker_ip: " << ip_  << ",kafka_consumer_topic: "<< topic_ << ",kafka_consumer_group: " << comsumer_group_ << " thread num " << thread_num_;
    if (ip_.size() == 0 || topic_.size() == 0 || comsumer_group_.size() == 0)
    {
        AK_LOG_FATAL << "KafkaConsumerInit kafka_broker_ip: " << ip_  << ",kafka_consumer_topic: "<< topic_ << ",kafka_consumer_group: " << comsumer_group_ << " thread num " << thread_num_;
    }

    cppkafka::Configuration config =
    {
        { "metadata.broker.list", ip_ },
        { "group.id", comsumer_group_ },
        { "enable.auto.commit", false },
        { "log_level", "6" },//7=debug 6=info
        { "auto.offset.reset", "earliest"},
        { "max.poll.interval.ms", max_poll_ms_}
    };
    
    config.set_log_callback([](cppkafka::KafkaHandleBase& handle,
                              int level,
                              const std::string& facility,
                              const std::string& message) {
        AK_LOG_INFO << "[Kafka][" << level << "][" << facility << "] " << message;
    });
    cppkafka::Consumer consumer(config);
    consumer.set_assignment_callback([this](const cppkafka::TopicPartitionList& partitions)
    {
        status_ = true;
        AK_LOG_INFO << "Got assigned: " << FormatPartitions(partitions);
    });

    consumer.set_revocation_callback([this](const cppkafka::TopicPartitionList& partitions)
    {
        status_ = true;
        AK_LOG_INFO << "Got revoked: " <<  FormatPartitions(partitions);
    });

    consumer.set_rebalance_error_callback([this](cppkafka::Error error) {
        AK_LOG_WARN << "rebalance_error: " <<error.to_string();
    });

    consumer.subscribe({topic_});
    status_ = true;
    while (1)
    {
        try
        {
            cppkafka::Message msg = consumer.poll(std::chrono::milliseconds(60 * 1000));
            if (msg)
            {
                if (msg.get_error())
                {
                    if (!msg.is_eof())
                    {
                        AK_LOG_INFO << "KafkaConsumer Received error notification: " << msg.get_error();
                    }
                    else
                    {
                        AK_LOG_INFO << "KafkaConsumer Received error notification is_eof: " << msg.get_error();
                    }
                    status_ = false;
                }
                else
                {
                    if (READ_OK_COMMIT_ == commit_type_)
                    {
                        consumer.commit(msg);            
                    }

                    AK_LOG_INFO << "KafkaConsumer partition " << msg.get_partition() << ", offset:"  << msg.get_offset() << " " << consumer.get_member_id(); 
                    
                    int64_t unread_messages = GetUnReadeNum(topic_, consumer, msg);
                    AK_LOG_INFO << "Unread messages: " << unread_messages;
                    
                    bool ok = cb_(msg.get_partition(), msg.get_offset(), msg.get_key(), msg.get_payload());
                    AK_LOG_INFO << "KafkaConsumer end";
                    
                    if (commit_type_ == CALLBACK_OK_COMMIT && ok)
                    {
                        consumer.commit(msg);
                    }
                    
                    status_ = true;
                }
            }
            else
            {
                AK_LOG_INFO << "KafkaConsumer poll 60s timeout " << consumer.get_member_id();
                status_ = true;
            }
        }
        catch(const std::exception& e)
        {
            status_ = false;
            AK_LOG_WARN << "AkcsKafkaConsumer catch exception: " << e.what();
        }
    }    
}


void AkcsKafkaConsumer::StartInnerConsumerBatch()
{
    setBatchReadeNumber(1);
    AK_LOG_INFO << "KafkaConsumerInit kafka_broker_ip: " << ip_  << ",kafka_consumer_topic: "<< topic_ << ",kafka_consumer_group: " << comsumer_group_ << " thread num " << thread_num_;
    if (ip_.size() == 0 || topic_.size() == 0 || comsumer_group_.size() == 0)
    {
        AK_LOG_FATAL << "KafkaConsumerInit kafka_broker_ip: " << ip_  << ",kafka_consumer_topic: "<< topic_ << ",kafka_consumer_group: " << comsumer_group_ << " thread num " << thread_num_;
    }
    cppkafka::Configuration config =
    {
        { "metadata.broker.list", ip_ },
        { "group.id", comsumer_group_ },
        { "enable.auto.commit", false },
        { "log_level", "6" }, //7=debug 6=info
        { "auto.offset.reset", "earliest"},
        { "max.poll.interval.ms", max_poll_ms_}
    };
    config.set_log_callback([](cppkafka::KafkaHandleBase& handle,
                              int level,
                              const std::string& facility,
                              const std::string& message) {
        AK_LOG_INFO << "[Kafka][" << level << "][" << facility << "] " << message;
    });    
    cppkafka::Consumer consumer(config);
    consumer.set_assignment_callback([this](const cppkafka::TopicPartitionList& partitions)
    {
        status_ = true;
        AK_LOG_INFO << "Got assigned: " << partitions;
    });

    consumer.set_revocation_callback([this](const cppkafka::TopicPartitionList& partitions)
    {
        status_ = true;
        AK_LOG_INFO << "Got revoked: " << partitions;
    });
    
    consumer.set_rebalance_error_callback([this](cppkafka::Error error) {
        AK_LOG_WARN << "rebalance_error: " <<error.to_string();
    });

    consumer.subscribe({topic_});
    status_ = true;
    while (1)
    {
        try
        {
            std::vector<cppkafka::Message> msg_list = consumer.poll_batch(comsumer_batch_num_, std::chrono::milliseconds(60 * 1000));
            if (msg_list.size() > 0)
            {
                cppkafka::Message& last_message = msg_list.back();
                if (READ_OK_COMMIT_ == commit_type_)
                {
                    consumer.commit(last_message);            
                }
                
                
                AK_LOG_INFO << "KafkaConsumer partition " << last_message.get_partition() << ", offset:"  << last_message.get_offset() << " "<< consumer.get_member_id(); 
                
                int64_t unread_messages = GetUnReadeNum(topic_, consumer, last_message);
                AK_LOG_INFO << "Unread messages: " << unread_messages;
                
                bool ok = batch_cb_(msg_list, unread_messages);
                AK_LOG_INFO << "KafkaConsumer end";
                
                if (commit_type_ == CALLBACK_OK_COMMIT && ok)
                {
                    consumer.commit(last_message);
                }
                
                status_ = true;
            }
            else
            {
                AK_LOG_INFO << "KafkaConsumer poll 60s timeout " << consumer.get_member_id();
            }
        }
        catch(const std::exception& e)
        {
            status_ = false;
            AK_LOG_WARN << "AkcsKafkaConsumer catch exception: " << e.what();
        }
    }    
}



void AkcsKafkaConsumer::Start()
{
    for (int i = 0; i < thread_num_; ++i)
    {
        std::thread thread(&AkcsKafkaConsumer::StartInner, this);
        thread.detach();
    }
}



void AkcsKafkaConsumer::StartConsumerBatch()
{
    for (int i = 0; i < thread_num_; ++i)
    {
        std::thread thread(&AkcsKafkaConsumer::StartInnerConsumerBatch, this);
        thread.detach();
    }
}


