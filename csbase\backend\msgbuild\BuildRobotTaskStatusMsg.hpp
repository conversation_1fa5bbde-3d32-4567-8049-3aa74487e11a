#ifndef __BUILD_ROBOT_TASK_STATUS_MSG_HPP__
#define __BUILD_ROBOT_TASK_STATUS_MSG_HPP__

#include "util.h"
#include <string>
#include "XmlMsgBuilder.h"
#include "AkLogging.h"

namespace akcs_msgbuild {

/*
发送机器人任务状态给室内机
<Msg>
<Type>RobotTaskStatus</Type>
    <Params>
        <TaskId></TaskId>  // 任务ID
        <Status></Status>  // 任务状态，0~10
        <WithdrawTimeout></WithdrawTimeout>  // 取物超时时间
   </Params>
</Msg>
*/
inline int BuildRobotTaskStatusMsgToDev(const std::string &task_id, RobotTaskStatus task_status, int withdraw_timeout, std::string &msg)
{
    XmlBuilder xml_builder("RobotTaskStatus");
    
    XmlKV tag_map;
    tag_map["TaskId"] = task_id;
    tag_map["Status"] = std::to_string((int)task_status);
    tag_map["WithdrawTimeout"] = std::to_string(withdraw_timeout);

    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildRobotTaskStatusMsgToDev msg:\n" << msg;
    return 0;
}

/*
发送机器人任务状态给APP
<Msg>
<Type>RobotTaskStatus</Type>
    <Params>
        <Status></Status>           // 任务状态，6 送物到达, 8 取物超时
        <PickupCode></PickupCode>   // 取物码
        <PickupDDL></PickupDDL>     // 取物截止时间
        <DeliveredTime></DeliveredTime>  // 送物到达时间
   </Params>
</Msg>
*/
inline int BuildRobotTaskStatusMsgToApp(RobotTaskStatus task_status, const std::string& account, int message_id, 
    const std::string &pickup_code, const std::string & pickup_ddl, const std::string & delivery_time, std::string &msg)
{
    XmlBuilder xml_builder("RobotTaskStatus");
    
    AK_LOG_INFO << "BuildRobotTaskStatusMsgToApp begin, task_status: " << (int) task_status;

    XmlKV tag_map;
    tag_map["Site"] = account;
    tag_map["MessageID"] = std::to_string(message_id);
    tag_map["Status"] = std::to_string((int)task_status);
    if (task_status == RobotTaskStatus::ARRIVED || task_status == RobotTaskStatus::DELIVERY_ARRIVED) {
        tag_map["PickupCode"] = pickup_code;
        tag_map["PickupDDL"] = pickup_ddl;
        tag_map["Status"] = std::to_string((int)RobotTaskStatus::DELIVERY_ARRIVED);
    } else if (task_status == RobotTaskStatus::PICKUP_TIMEOUT) {
        tag_map["DeliveredTime"] = delivery_time;
    }

    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildRobotTaskStatusMsgToApp msg:\n" << msg;
    return 0;
}

} // namespace CMsgBuildHandle

#endif // __BUILD_ROBOT_TASK_STATUS_MSG_HPP__ 