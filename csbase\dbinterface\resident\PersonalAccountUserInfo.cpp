#include <sstream>
#include <string.h>
#include "util.h"
#include "AkLogging.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "ConnectionManager.h"

namespace dbinterface
{

// encrypt field : Email、MobileNumber
static const std::string per_account_sec = " ID,Email,MobileNumber,Passwd,AppMainUserAccount,AppLastLoginUserAccount,UUID,CreateTime ";


PersonalAccountUserInfo::PersonalAccountUserInfo()
{

}

void PersonalAccountUserInfo::GetAccountFromSql(PerAccountUserInfo &account, CRldbQuery& query)
{
    account.id = ATOI(query.GetRowData(0));
    Snprintf(account.email, sizeof(account.email), dbinterface::DataConfusion::Decrypt(query.GetRowData(1)).c_str());
    Snprintf(account.mobile_number, sizeof(account.mobile_number), dbinterface::DataConfusion::Decrypt(query.GetRowData(2)).c_str());
    Snprintf(account.passwd, sizeof(account.passwd), query.GetRowData(3));
    Snprintf(account.main_user_account, sizeof(account.main_user_account), query.GetRowData(4));
    Snprintf(account.last_login_account, sizeof(account.last_login_account), query.GetRowData(5));
    Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(6));
    Snprintf(account.create_time, sizeof(account.create_time), query.GetRowData(7));
    return;
}

int PersonalAccountUserInfo::GetPerAccountInfoByUUID(const std::string& uuid, PerAccountUserInfo &account)
{
    std::stringstream streamSQL;
    streamSQL << "select " << per_account_sec << " from PersonalAccountUserInfo where UUID = '" << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int PersonalAccountUserInfo::GetPerAccountInfoByEmail(const std::string& email, PerAccountUserInfo &account)
{
    std::string encrypt_email = dbinterface::DataConfusion::Encrypt(email.c_str());
    
    auto pos = email.find('@');
    if (pos == std::string::npos)
    {
        return -1;
    }    
    std::stringstream streamSQL;
    streamSQL << "select " << per_account_sec << " from PersonalAccountUserInfo where Email = '" << encrypt_email <<"' limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}
int PersonalAccountUserInfo::GetPerAccountInfoByEmailFromMasterDB(const std::string& email, PerAccountUserInfo &account)
{
    auto pos = email.find('@');
    if (pos == std::string::npos)
    {
        return -1;
    }      
    
    std::string encrypt_email = dbinterface::DataConfusion::Encrypt(email.c_str());
    
    std::stringstream streamSQL;
    streamSQL << "/*master*/ select " << per_account_sec << " from PersonalAccountUserInfo where Email = '" << encrypt_email <<"' limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int PersonalAccountUserInfo::GetPerAccountInfoByMobileNumber(const std::string& mobile_number, PerAccountUserInfo &account)
{
    if (mobile_number.length() < 5)//定一个长度防止传入空格或者空字符串导致查出很多账号
    {
        return -1;
    }
    
    std::string encrypt_mobile_number = dbinterface::DataConfusion::Encrypt(mobile_number.c_str());
    
    std::stringstream streamSQL;
    streamSQL << "select " << per_account_sec << " from PersonalAccountUserInfo where MobileNumber = '" << encrypt_mobile_number << "' limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int PersonalAccountUserInfo::GetPerAccountInfoByMobileFromMasterDB(const std::string& mobile_number, PerAccountUserInfo &account)
{
    if (mobile_number.length() < 5)
    {
        return -1;
    }    
    
    std::string encrypt_mobile_number = dbinterface::DataConfusion::Encrypt(mobile_number.c_str());

    std::stringstream streamSQL;
    streamSQL << "/*master*/ select " << per_account_sec << " from PersonalAccountUserInfo where MobileNumber = '" << encrypt_mobile_number <<"' limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(const std::string& login_account, PerAccountUserInfo& account)
{
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    //判断是否是邮箱
    if (login_account.find('@') != std::string::npos)
    {
        // 邮箱登录会有大小写问题, 例如: <EMAIL> 和 <EMAIL>
        // 必须要先通过mapping库查出所有邮箱, 再用加密后的邮箱去匹配用户
        std::vector<std::string> encrypt_email_list;
            
        std::stringstream stream_sql_mapping;
        stream_sql_mapping << "/*master*/ select EnColumn from AKCSMapping.EmailMapping where DeColumn = '" << login_account << "'";
        
        query.Query(stream_sql_mapping.str());
        while (query.MoveToNextRow())
        {
            std::string email_encolumn = query.GetRowData(0);
            encrypt_email_list.push_back(email_encolumn);
        }

        // 使用邮箱登录
        if (encrypt_email_list.size() > 0)
        {
            // 遍历mapping库中所有的邮箱,找到匹配上用户的那个邮箱
            for (const auto& encrypt_email : encrypt_email_list)
            {
                std::stringstream stream_sql;
                stream_sql << "select " << per_account_sec << " from PersonalAccountUserInfo where Email = '" << encrypt_email << "'";
                
                query.Query(stream_sql.str());
                if (query.MoveToNextRow())
                {
                    GetAccountFromSql(account, query);
                    return 0;
                }
            }
        }
    }
    else
    {
        // 使用sip账号或者手机号登录
        std::string encrypt_login_account = dbinterface::DataConfusion::Encrypt(login_account.c_str());
        
        std::stringstream stream_sql;
        stream_sql << "select " << per_account_sec << " from PersonalAccountUserInfo where (MobileNumber = '" << encrypt_login_account << "' or AppMainUserAccount = '" << login_account << "') limit 1";
        
        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            GetAccountFromSql(account, query);
            if (strncmp(account.main_user_account, login_account.c_str(), login_account.length()) == 0)
            {
                account.sip_login = 1;
            }
            return 0;
        }
    }

    return -1;
}

int PersonalAccountUserInfo::GetPerAccountInfoByAccount(const std::string& account, PerAccountUserInfo& user_info)
{
    std::string userinfo_uuid = dbinterface::ResidentPersonalAccount::GetUserInfoUUIDByAccount(account);
    if (userinfo_uuid.length() <= 0)
    {
        return -1;
    }    
    return GetPerAccountInfoByUUID(userinfo_uuid, user_info); 
}

//根据account获取主站点account，可能是enduser或pm
int PersonalAccountUserInfo::GetMainAccountByAccount(const std::string& account, std::string& main_account)
{
    std::string userinfo_uuid = dbinterface::ResidentPersonalAccount::GetUserInfoUUIDByAccount(account);
    if (userinfo_uuid.length() <= 0)
    {
        return -1;
    }
    PerAccountUserInfo per_user_info;
    UserInfoAccount user_info;
    if(0 == GetPerAccountInfoByUUID(userinfo_uuid, per_user_info))
    {
        main_account = per_user_info.main_user_account;
        return 0;
    }
    else if(0 == dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(userinfo_uuid, user_info))
    {
        main_account = user_info.main_user_account;
        return 0;
    }
    return -1;
}

//根据account_uuid获取主站点account，可能是enduser或pm
int PersonalAccountUserInfo::GetMainAccountByAccountUUID(const std::string& uuid, std::string& main_account)
{
    ResidentPerAccount account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(uuid, account))
    {
        return -1;
    }
    
    PerAccountUserInfo per_user_info;
    UserInfoAccount user_info;
    if(0 == GetPerAccountInfoByUUID(account.user_info_uuid, per_user_info))
    {
        main_account = per_user_info.main_user_account;
        return 0;
    }
    else if(0 == dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(account.user_info_uuid, user_info))
    {
        main_account = user_info.main_user_account;
        return 0;
    }
    return -1;
}

//根据account_uuid获取主站点account_uuid，可能是enduser或pm
int PersonalAccountUserInfo::GetMainAccountUUIDByAccountUUID(const std::string& uuid, std::string& main_account_uuid)
{
    std::string main_account;
    if (0 != dbinterface::PersonalAccountUserInfo::GetMainAccountByAccountUUID(uuid, main_account))
    {
        return -1;
    }
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDByAccount(main_account, main_account_uuid))
    {
        return -1;
    }
    return 0;
}


//根据PersonalAccount 的uuid找主站点的用户信息
int PersonalAccountUserInfo::GetMainAccountInfoByPerAccountUUID(const std::string& personal_account_uuid, PersonalAccountNodeInfo& master)
{
    ResidentPerAccount account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account_uuid, account) )
    {
        return -1;
    }

    std::string main_account;
    PerAccountUserInfo per_user_info;
    if(0 == GetPerAccountInfoByUUID(account.user_info_uuid, per_user_info))
    {
        if(strcmp(per_user_info.main_user_account, account.account) != 0
            && 0 != dbinterface::ResidentPersonalAccount::GetUserAccount(per_user_info.main_user_account, account))
        {
            return -1;
        }
        Snprintf(master.node, sizeof(master.node), account.account);
        master.conn_type = account.conn_type;     
        return 0;
    }
    return -1;
}

int PersonalAccountUserInfo::GetPmMainSitesByAppList(const ResidentPerAccountList& pm_app_list, std::vector<std::string>& pm_main_sites)
{
    for (const auto& pm_app : pm_app_list)
    {
        UserInfoAccount account_userinfo;
        if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(pm_app.user_info_uuid, account_userinfo))
        {
            pm_main_sites.push_back(account_userinfo.main_user_account);
        }
    }
    return 0;
}

//pm_all_sites: pm所有站点列表<主站点site, 该社区对应实际pm站点的site>
void PersonalAccountUserInfo::GetPmAllSitesByAppList(const ResidentPerAccountList& pm_app_list, std::map<std::string, std::string>& pm_all_sites)
{
    for (const auto& pm_app : pm_app_list)
    {
        UserInfoAccount account_userinfo;
        if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(pm_app.user_info_uuid, account_userinfo))
        {
            std::pair<std::string,std::string> pm_pair(account_userinfo.main_user_account, pm_app.account);
            pm_all_sites.insert(pm_pair);
        }
    }
}

int PersonalAccountUserInfo::GetCurrentSiteByMainSite(const std::string& main_site, std::string& current_site)
{
    PerAccountUserInfo account_user_info;
    if(dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByAccount(main_site, account_user_info) != 0)
    {
        AK_LOG_WARN << "get account user info failed, account is: " << main_site;
        return -1;
    }
    current_site = account_user_info.main_user_account;
    if(strlen(account_user_info.last_login_account) != 0)
    {
        current_site = account_user_info.last_login_account;
    }
    return 0;
}

}

