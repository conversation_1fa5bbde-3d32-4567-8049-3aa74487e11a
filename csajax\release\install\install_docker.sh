#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME=csajax

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

APP_NAME=csajax   # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csajax
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
LOG_PATH=/var/log/csajaxlog
RUN_SCRIPT=csajaxrun.sh
SIGNAL=${SIGNAL:-TERM}

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME."


if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

echo '读取配置'

ENV_CONF_PARAM="
-e ENABLE_AKCS_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
-e DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
-e MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e GATEWAY_NUM=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
-e AJAX_GLOBAL_KAFKA_IP=$(grep_conf 'AJAX_GLOBAL_KAFKA_IP' $INSTALL_CONF)
-e WEB_DOMAIN=$(grep_conf 'WEB_DOMAIN' $INSTALL_CONF)
"

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

ENV_LOAD_PARAM="
-v /usr/share/zoneinfo:/usr/share/zoneinfo
-v /var/log/csajaxlog:/var/log/csajaxlog
-v /var/core:/var/core
-v /etc/ip:/etc/ip
-v /etc/kdc.conf:/etc/kdc.conf
-v /bin/crypto:/bin/crypto
"

echo ${ENV_CONF_PARAM};

# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------


if [ `docker ps -a | grep -w $CONTAINER_NAME | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true
fi

docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} ${ENV_LOAD_PARAM} --restart=always --net=host --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csajax/scripts/${RUN_SCRIPT}

#守护进程中会进行环境变量替换配置文件中的内容
#具体看cspdu2kafkamq/scripts/sedconf.sh
