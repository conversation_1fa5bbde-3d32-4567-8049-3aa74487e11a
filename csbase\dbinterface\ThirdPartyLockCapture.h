#ifndef __DB_THIRD_PARTY_LOCK_CAPTURE_H__
#define __DB_THIRD_PARTY_LOCK_CAPTURE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/ThirdPartyLockDevice.h"

typedef struct ThirdPartyLockCaptureInfo_T
{
    char mac[32];
    char pic_name[256];
    char lock_name[64];
    char initiator[128];
    char capture_time[32];
    int status;
    char personal_account_uuid[36];
    char personal_account_uuid_for_operator[36];
    int capture_type;
    int response;
    ThirdPartyLockType lock_type;
    ThirdPartyLockCaptureInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} ThirdPartyLockCaptureInfo;

namespace dbinterface {

class ThirdPartyLockCapture
{
public:
    static int InsertThirdPartyLockCapture(const ThirdPartyLockCaptureInfo& third_party_lock_capture_info);
private:
    ThirdPartyLockCapture() = delete;
    ~ThirdPartyLockCapture() = delete;
    static void GetThirdPartyLockCaptureFromSql(ThirdPartyLockCaptureInfo& third_party_lock_capture_info, CRldbQuery& query);
};

}
#endif