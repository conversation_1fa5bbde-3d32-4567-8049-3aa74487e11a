#ifndef __REDIS_SFAE_CACHEPOOL_H__
#define __REDIS_SFAE_CACHEPOOL_H__

#include <map>
#include <string>
#include <vector>
#include "CachePool.h"
#include "AkLogging.h"

// RAII操作CacheConn
class SafeCacheConn {
public:
    explicit SafeCacheConn(const std::string& cache_db) : cache_manager_(CacheManager::getInstance()) { 
        connection_ = cache_manager_->GetCacheConn(cache_db.c_str());
    }

    ~SafeCacheConn() {
        if (cache_manager_) {
            cache_manager_->RelCacheConn(connection_);
        }
    }

    bool isConnect()
    {
        return connection_ != nullptr;
    }

    std::string set(const string &key, const string& value);

    std::string setex(const std::string& key, int timeout, const std::string& value);

    std::string get(const std::string& key); 

    long expire(const std::string& key, int second);

    bool isExists(const std::string &key);

    std::string eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args) ;

    bool eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args, std::map<std::string, std::string>& ret_value);
    
    long del(const std::string& key);
    
    bool mget(const std::vector<string>& keys, std::map<std::string, std::string>& ret_value);
    
    long hdel(const std::string& key, const std::string& field);
    
    std::string hget(const std::string& key, const std::string& field);
    
    bool hgetAll(const std::string& key, std::map<std::string, std::string>& ret_value);
    
    long hset(const std::string& key, const std::string& field, const std::string& value);

    std::string hmset(const std::string& key, std::map<std::string, std::string>& hash);

    long incr(const std::string& key);
    
    bool flushdb();
    
    long zadd(const std::string& key, const std::string& score, const std::string& member);
    
    long zrem(const std::string& key, const std::string& member);
    
    std::string zscore(const std::string& key, const std::string& member);
    
    std::vector<std::string> zrangebyscore(const std::string& key, uint64_t min, uint64_t max);
private:
    CacheManager* cache_manager_;
    CacheConn* connection_;
};

#endif /* __REDIS_SFAE_CACHEPOOL_H__ */
