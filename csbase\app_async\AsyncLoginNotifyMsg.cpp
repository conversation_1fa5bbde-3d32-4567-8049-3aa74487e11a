#include "AsyncLoginNotifyMsg.h"
#include "json/json.h"
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "backend/MsgBuild.h"
#include "util.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"

std::string AsyncLoginNotifyMsg::BuildAsyncMsg()
{
    Json::Value json_datas;
    json_datas["old_login_version"] = old_version_;
    return BaseBuildAsyncMsg(json_datas);
}
