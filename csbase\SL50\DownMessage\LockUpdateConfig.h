#ifndef __LOCK_UPDATE_CONFIG_H_
#define __LOCK_UPDATE_CONFIG_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

enum class LockUpdateConfigType {
    ALL = 0, // 默认全部更新
    LANGUAGE = 1,
    VOLUME = 2,
    DWELL_ALARM = 3,
    DWELL_TIME = 4,
    MONITORING_SCOPE = 5,
    DOUBLE_VERIFICATION = 6,
};

class LockUpdateConfig : public BaseParam, public ServiceCall {
public:
    // 业务参数
    std::string entity_id_;
    std::string language_;
    std::string volume_;
    bool dwell_alarm_ = false;
    int dwell_time_ = 0;
    std::string monitoring_scope_;
    bool double_verification_ = false;

    static constexpr const char* DEFAULT_SERVICE_TYPE = "call_service";
    static constexpr const char* DEFAULT_SERVICE_DOMAIN = "lock";
    static constexpr const char* DEFAULT_SERVICE_NAME = "lock_update_config";
    static constexpr const char* COMMOND = "v1.0_d_device_ha_control";
    static constexpr const char* AKCS_COMMAND = "v1.0_d_device_ha_control_lock_update_config";

    LockUpdateConfig();
    LockUpdateConfig(const ServiceCall& sc);

    std::string to_json();
    std::string to_json(LockUpdateConfigType config_type);
    void from_json(const std::string& json_str);
};

#endif