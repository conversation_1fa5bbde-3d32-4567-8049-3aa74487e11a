#ifndef __CSYUNJIROBOT_UTIL_H__
#define __CSYUNJIROBOT_UTIL_H__

#include "util.h"
#include "SHA1.h"
#include "Base64.h"
#include "AES256.h"
#include "util_time.h"
#include "util_string.h"
#include "AkcsCommonDef.h"
#include "SafeCacheConn.h"
#include "YunJiRobotDefine.h"
#include "YunJiRobotConfig.h"
#include <json/json.h>

extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;

// 认证信息结构体
struct YunJiAuthInfo
{
    std::string signature;
    std::string signature_nonce;
    std::string access_key_id;
    std::string timestamp;
};

// 访问令牌数据结构体
struct YunJiAccessTokenData
{
    std::string access_token;
    std::string expiration;
    std::string creation;
    std::string store_id;
    std::string access_key_id;
    std::string access_key_secret;

    // 构造函数
    YunJiAccessTokenData() = default;

    // 从JSON解析
    bool FromJson(const Json::Value& json_data, const std::string& key_secret)
    {
        if (!json_data.isObject())
        {
            return false;
        }

        access_token = json_data.get("accessToken", "").asString();
        expiration = json_data.get("expiration", "").asString();
        creation = json_data.get("creation", "").asString();

        // store_id和storeId都支持
        if (json_data.isMember("store_id"))
        {
            store_id = json_data["store_id"].asString();
        }
        else if (json_data.isMember("storeId"))
        {
            store_id = json_data["storeId"].asString();
        }

        access_key_id = json_data.get("accessKeyId", "").asString();

        access_key_secret = key_secret;
        return !access_token.empty();
    }

    // 转换为JSON字符串（用于调试）
    std::string ToJsonString() const
    {
        Json::Value json_obj;
        json_obj["accessToken"] = access_token;
        json_obj["expiration"] = expiration;
        json_obj["creation"] = creation;
        json_obj["store_id"] = store_id;
        json_obj["accessKeyId"] = access_key_id;
        json_obj["accessKeyToken"] = access_key_secret;

        Json::FastWriter writer;
        return writer.write(json_obj);
    }
};

// 商品信息结构体
struct GoodsItem
{
    std::string goods_id;
    std::string out_goods_id;
    std::string goods_name;
    int quantity;

    GoodsItem() : quantity(0) {}
    GoodsItem(const std::string& id, const std::string& name, int qty)
        : goods_id(id), goods_name(name), quantity(qty) {}

    Json::Value ToJson() const
    {
        Json::Value json_obj;
        json_obj["goodsId"] = goods_id;
        json_obj["outGoodsId"] = out_goods_id;
        json_obj["goodsName"] = goods_name;
        json_obj["quantity"] = quantity;
        return json_obj;
    }

    bool FromJson(const Json::Value& json_data)
    {
        if (!json_data.isObject())
        {
            return false;
        }

        goods_id = json_data.get("goodsId", "").asString();
        out_goods_id = json_data.get("outGoodsId", "").asString();
        goods_name = json_data.get("goodsName", "").asString();
        quantity = json_data.get("quantity", 0).asInt();

        return !goods_id.empty();
    }
};

// 任务额外信息结构体
struct TaskExtraInfo
{
    std::string phone;
    std::vector<GoodsItem> goods;

    TaskExtraInfo() = default;

    Json::Value ToJson() const
    {
        Json::Value json_obj;
        json_obj["phone"] = phone;

        Json::Value goods_array(Json::arrayValue);
        for (const auto& item : goods)
        {
            goods_array.append(item.ToJson());
        }
        json_obj["goods"] = goods_array;

        return json_obj;
    }

    bool FromJson(const Json::Value& json_data)
    {
        if (!json_data.isObject())
        {
            return false;
        }

        phone = json_data.get("phone", "").asString();

        // 解析商品列表
        if (json_data.isMember("goods") && json_data["goods"].isArray())
        {
            const Json::Value& goods_array = json_data["goods"];
            goods.clear();
            goods.reserve(goods_array.size());

            for (const auto& goods_json : goods_array)
            {
                GoodsItem item;
                if (item.FromJson(goods_json))
                {
                    goods.push_back(item);
                }
            }
        }

        return true;
    }
};

// 任务信息结构体
struct TaskInfo
{
    std::string task_id;
    uint64_t created_at;
    uint64_t updated_at;
    std::string status;
    std::string task_type;
    std::string attach;
    std::string store_id;
    std::string out_task_id;
    std::string target;
    TaskExtraInfo extra;

    TaskInfo() : created_at(0), updated_at(0) {}

    bool FromJson(const Json::Value& json_data)
    {
        if (!json_data.isObject())
        {
            return false;
        }

        task_id = json_data.get("taskId", "").asString();
        created_at = json_data.get("createdAt", 0).asUInt64();
        updated_at = json_data.get("updatedAt", 0).asUInt64();
        status = json_data.get("status", "").asString();
        task_type = json_data.get("taskType", "").asString();
        attach = json_data.get("attach", "").asString();
        store_id = json_data.get("storeId", "").asString();
        out_task_id = json_data.get("outTaskId", "").asString();
        target = json_data.get("target", "").asString();

        // 解析额外信息
        if (json_data.isMember("extra"))
        {
            extra.FromJson(json_data["extra"]);
        }

        return !task_id.empty();
    }

    std::string ToJsonString() const
    {
        Json::Value json_obj;
        json_obj["taskId"] = task_id;
        json_obj["createdAt"] = created_at;
        json_obj["updatedAt"] = updated_at;
        json_obj["status"] = status;
        json_obj["taskType"] = task_type;
        json_obj["attach"] = attach;
        json_obj["storeId"] = store_id;
        json_obj["outTaskId"] = out_task_id;
        json_obj["target"] = target;
        json_obj["extra"] = extra.ToJson();

        Json::FastWriter writer;
        return writer.write(json_obj);
    }
};

// 任务列表响应结构体
struct TaskListResponse
{
    int code;
    std::string message;
    std::string request_id;
    std::string customer_error_message;
    std::vector<TaskInfo> tasks;

    TaskListResponse() : code(-1) {}

    bool FromJson(const Json::Value& json_root)
    {
        if (!json_root.isObject())
        {
            return false;
        }

        code = json_root.get("code", -1).asInt();
        message = json_root.get("message", "").asString();
        request_id = json_root.get("requestId", "").asString();
        customer_error_message = json_root.get("customerErrorMessage", "").asString();

        // 解析任务列表
        if (json_root.isMember("data") && json_root["data"].isArray())
        {
            const Json::Value& data_array = json_root["data"];
            tasks.clear();
            tasks.reserve(data_array.size());

            for (const auto& task_json : data_array)
            {
                TaskInfo task;
                if (task.FromJson(task_json))
                {
                    tasks.push_back(task);
                }
            }
        }

        return code == 0;
    }

    bool IsSuccess() const
    {
        return code == 0;
    }
};

// 商户呼叫请求结构体
struct MerchantCallRequest
{
    std::string store_id;
    std::string out_task_id;
    std::string target;
    std::string via;
    std::vector<GoodsItem> goods;

    MerchantCallRequest() = default;

    std::string ToJsonString() const
    {
        Json::Value json_obj;
        json_obj["storeId"] = store_id;
        json_obj["outTaskId"] = out_task_id;
        json_obj["target"] = target;
        json_obj["via"] = via;

        Json::Value goods_array(Json::arrayValue);
        for (const auto& item : goods)
        {
            goods_array.append(item.ToJson());
        }
        json_obj["goods"] = goods_array;

        Json::FastWriter writer;
        return writer.write(json_obj);
    }
};

// 商户呼叫响应结构体
struct MerchantCallResponse
{
    int code;
    std::string message;
    std::string task_id;
    std::string request_id;
    std::string customer_error_message;

    MerchantCallResponse() : code(-1) {}

    bool FromJson(const Json::Value& json_root)
    {
        if (!json_root.isObject())
        {
            return false;
        }

        code = json_root.get("code", -1).asInt();
        message = json_root.get("message", "").asString();
        task_id = json_root.get("taskId", "").asString();
        request_id = json_root.get("requestId", "").asString();
        customer_error_message = json_root.get("customerErrorMessage", "").asString();

        return code == 0;
    }

    bool IsSuccess() const
    {
        return code == 0;
    }
};

// 取消任务请求结构体
struct CancelRobotTaskRequest
{
    std::string new_status;  // "cancelled"
    std::string robot_id;    // 机器人ID(可选)
    std::string option;      // "BACK_DEFAULT_ORIGIN" 或 "DONT_BACK_DEFAULT_ORIGIN"

    CancelRobotTaskRequest() : new_status("cancelled"), option("BACK_DEFAULT_ORIGIN") {}

    std::string ToJsonString() const
    {
        Json::Value json_obj;
        json_obj["newStatus"] = new_status;
        if (!robot_id.empty()) {
            json_obj["robotId"] = robot_id;
        }
        json_obj["option"] = option;

        Json::FastWriter writer;
        return writer.write(json_obj);
    }
};

struct CancelInQueenRobotTaskRequest
{
    std::string store_id;    // 门店编号
    std::string task_id;     // 云迹生成的任务ID

    CancelInQueenRobotTaskRequest() {}

    CancelInQueenRobotTaskRequest(const std::string& store_id, const std::string& task_id)
        : store_id(store_id), task_id(task_id) {}

    std::string ToJsonString() const
    {
        Json::Value json_obj;
        json_obj["storeId"] = store_id;
        json_obj["taskId"] = task_id;
        json_obj["overtime"] = 30;
        json_obj["overtimeEvent"] = "back";

        Json::FastWriter writer;
        return writer.write(json_obj);
    }
};

// 取消任务响应结构体
struct CancelRobotTaskResponse
{
    int code;
    std::string message;
    std::string request_id;
    std::string customer_error_message;

    CancelRobotTaskResponse() : code(-1) {}

    bool FromJson(const Json::Value& json_root)
    {
        if (!json_root.isObject())
        {
            return false;
        }

        code = json_root.get("code", -1).asInt();
        message = json_root.get("message", "").asString();
        request_id = json_root.get("requestId", "").asString();
        customer_error_message = json_root.get("customerErrorMessage", "").asString();

        return code == 0;
    }

    bool IsSuccess() const
    {
        return code == 0;
    }
};

// 设备信息结构体
struct DeviceInfo
{
    std::string device_id;
    std::string device_name;
    std::string device_serial_number;
    std::string device_type;
    std::string product_id;
    std::string product_name;
    std::string store_id;

    DeviceInfo() = default;

    bool FromJson(const Json::Value& json_data)
    {
        if (!json_data.isObject())
        {
            return false;
        }

        device_id = json_data.get("deviceId", "").asString();
        device_name = json_data.get("deviceName", "").asString();
        device_serial_number = json_data.get("deviceSerialNumber", "").asString();
        device_type = json_data.get("deviceType", "").asString();
        product_id = json_data.get("productId", "").asString();
        product_name = json_data.get("productName", "").asString();
        store_id = json_data.get("storeId", "").asString();

        return !device_id.empty();
    }

    std::string ToJsonString() const
    {
        Json::Value json_obj;
        json_obj["deviceId"] = device_id;
        json_obj["deviceName"] = device_name;
        json_obj["deviceSerialNumber"] = device_serial_number;
        json_obj["deviceType"] = device_type;
        json_obj["productId"] = product_id;
        json_obj["productName"] = product_name;
        json_obj["storeId"] = store_id;

        Json::FastWriter writer;
        return writer.write(json_obj);
    }
};

// 设备列表响应结构体
struct DeviceListResponse
{
    int code;
    std::string message;
    std::string request_id;
    std::string customer_error_message;
    int current;
    int page_size;
    int total;
    std::vector<DeviceInfo> devices;

    DeviceListResponse() : code(-1), current(0), page_size(0), total(0) {}

    bool FromJson(const Json::Value& json_root)
    {
        if (!json_root.isObject())
        {
            return false;
        }

        code = json_root.get("code", -1).asInt();
        message = json_root.get("message", "").asString();
        request_id = json_root.get("requestId", "").asString();
        customer_error_message = json_root.get("customerErrorMessage", "").asString();
        current = json_root.get("current", 0).asInt();
        page_size = json_root.get("pageSize", 0).asInt();
        total = json_root.get("total", 0).asInt();

        // 解析设备列表
        if (json_root.isMember("data") && json_root["data"].isArray())
        {
            const Json::Value& data_array = json_root["data"];
            devices.clear();
            devices.reserve(data_array.size());

            for (const auto& device_json : data_array)
            {
                DeviceInfo device;
                if (device.FromJson(device_json))
                {
                    devices.push_back(device);
                }
            }
        }

        return code == 0;
    }

    bool IsSuccess() const
    {
        return code == 0;
    }
};

class YunJiRobotApiUtil
{
public:
    // 生成完整的认证信息
    static YunJiAuthInfo YunJiApiSignature(const std::string& access_key_id, const std::string& access_key_secret)
    {
        YunJiAuthInfo auth_info;
        auth_info.signature_nonce = GetNbitRandomString(36);
        auth_info.timestamp = GetISO8601ChinaTimestampString();
        auth_info.access_key_id = access_key_id;

        std::string signature_key = access_key_secret + std::string("&");
        std::string to_sign = "accessKeyId=" + access_key_id + "&signatureNonce=" + auth_info.signature_nonce + "&timestamp=" + auth_info.timestamp;
        auth_info.signature = GetBase64Str(HMACSHA1::compute_bin(signature_key, to_sign));

        return auth_info;
    }

    // 构造认证JSON请求体
    static std::string BuildAuthRequestBody(const YunJiAuthInfo& auth_info)
    {
        Json::Value request_body;
        request_body["signature"] = auth_info.signature;
        request_body["signatureNonce"] = auth_info.signature_nonce;
        request_body["accessKeyId"] = auth_info.access_key_id;
        request_body["timestamp"] = auth_info.timestamp;

        Json::FastWriter writer;
        return writer.write(request_body);
    }

    // 构造认证头部列表
    static std::vector<std::string> BuildAuthHeaders(const YunJiAuthInfo& auth_info, const std::string& access_token)
    {
        std::vector<std::string> headers;

        headers.push_back("signatureNonce: " + auth_info.signature_nonce);
        headers.push_back("timestamp: " + auth_info.timestamp);
        headers.push_back("accessKeyId: " + auth_info.access_key_id);
        headers.push_back("signature: " + auth_info.signature);
        headers.push_back("token: " + access_token);

        return headers;
    }

private:
    YunJiRobotApiUtil() = delete;
    ~YunJiRobotApiUtil() = delete;
};


#endif
