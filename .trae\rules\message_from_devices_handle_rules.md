BackendFactory工厂模式处理文件生成使用方法：
请根据backend_factory_generation_rules.md中的规则，为以下XML信令生成C++ BackendFactory处理文件：
[你的XML信令语句]
该项目是社区、单住户项目（办公项目）。请生成对应的.h、.cpp和.hpp文件，实现IParseXml、IControl、IReplyMsg、IPushNotify、IPushThirdNotify、IToRouteMsg等接口函数。严格按照下面要求。


BackendFactory代码生成规则

生成代码前，请先读取以下文件了解具体的生成逻辑和实现细节：
- csresid/src/main/ReportMailboxArrivalNotice.h 和 ReportMailboxArrivalNotice.cpp（模板文件）
- csbase/backend/msgparse/ParseReportMailboxArrivalNotice.hpp（解析模板文件）
- csresid/src/core/BackendFactory.cpp（工厂模式实现）

输入要求：
- 需要给定一个XML信令格式
- 消息类型和业务场景

输出文件生成规则：
1. 头文件(.h)和源文件(.cpp)生成目录根据业务场景确定：
   - 社区/单住户：生成在csresid/src/main/目录下
   - 办公：生成在csoffice/src/main/目录下
2. 解析文件(.hpp)生成在csbase/backend/msgparse/目录下
3. 文件命名规则：
   - 头文件：处理类名.h
   - 源文件：处理类名.cpp
   - 解析文件：Parse处理类名.hpp
4. 头文件保护宏命名规则：
   - 格式：__处理类名大写_H__
   - 类名转换为大写并用下划线分隔
5. 类命名规则：
   - 处理类：根据消息类型命名，如ReportMailboxArrivalNotice
   - 解析函数：Parse处理类名
6. 函数命名规则：
   - 注册函数：RegFunc（自动注册到BackendFactory）
   - 接口函数：IParseXml、IControl、IReplyMsg、IPushNotify、IPushThirdNotify、IToRouteMsg
   - 解析函数：Parse处理类名
7. 代码结构要求：
   - 头文件包含必要的include和类定义
   - 源文件包含完整的处理函数实现和自动注册
   - 解析文件包含XML解析逻辑
   - 必须继承IBase接口并实现所有虚函数
8. 功能实现要求：
   - 在IParseXml中正确解析XML参数，调用对应的解析函数
   - 在IControl中实现主要业务处理逻辑
   - 其他接口函数（IReplyMsg、IPushNotify、IPushThirdNotify、IToRouteMsg）只提供最基本的模板实现：
     - IReplyMsg：构建简单的JSON回复消息
     - IPushNotify：基本的推送通知模板
     - IPushThirdNotify：基本的第三方推送模板
     - IToRouteMsg：基本的路由消息模板
   - 使用正确的消息ID和注册方式
9. 自动注册机制：
    ```cpp
    __attribute__((constructor))  static void Init(){
        IBasePtr p = std::make_shared<YourHandler>();
        RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_消息ID);
    };
    ```
10. 重要要求：
    - 严格按照模板文件的结构和命名规范
    - 保持代码风格一致
    - 确保所有必要的include都已包含
    - 在IParseXml和IControl中实现具体业务逻辑
    - 其他接口函数只提供最基本的模板实现，不要过多复杂逻辑
    - 正确处理XML解析和参数提取
    - 实现必要的错误处理和日志记录
    - 确保消息ID和消息类型的一致性 
    - XML_NODE_NAME_MSG_PARAM_xxxxxxxxxx等常量已在DclientMsgDef.h中定义。
    - 严禁使用atoi、strcpy等不安全函数，解析整型参数时请使用项目封装的ATOI。 