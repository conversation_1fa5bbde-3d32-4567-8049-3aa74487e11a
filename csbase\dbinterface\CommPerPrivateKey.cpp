#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/CommPerPrivateKey.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "util.h"

namespace dbinterface
{

CommPerPrivateKey::CommPerPrivateKey()
{

}

void CommPerPrivateKey::GetAccountPrivatekeyList(const std::string& users, UsersPinInfoMap& pm_create_key_list, UsersPinInfoMap& user_create_key_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    std::vector<std::string> vec;
    std::vector<std::string> s_vec;
    std::stringstream str_sql;
    CRldbQuery query(tmp_conn);
    str_sql << "select Code,Special,Account From CommPerPrivateKey where Account in(" << users << ");";
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string code = query.GetRowData(0);
        std::string uid = query.GetRowData(2);
        int special = ATOI(query.GetRowData(1));

        if (special)
        {
            UsersPinInfoMapIter iter = user_create_key_list.find(uid);
            if (iter != user_create_key_list.end())
            {
                iter->second.push_back(code);
            }
            else
            {
                std::vector<std::string> vec;
                vec.push_back(code);
                user_create_key_list.insert(std::make_pair(uid, vec));            
            }
            continue;
        }
        
        UsersPinInfoMapIter iter = pm_create_key_list.find(uid);
        if (iter != pm_create_key_list.end())
        {
            iter->second.push_back(code);
        }
        else
        {
            std::vector<std::string> vec;
            vec.push_back(code);
            pm_create_key_list.insert(std::make_pair(uid, vec));            
        }
    }
    ReleaseDBConn(conn);
    return;
}

void CommPerPrivateKey::GetOrderedAccountPrivatekeyList(const std::string& users, UserPinInfoList& pin_list)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn)

    CRldbQuery query(db_conn.get());
    std::stringstream str_sql;
    str_sql << "select Code,Special,Account From CommPerPrivateKey where Account in(" << users << ") order by ID;";
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserPinInfo pin_info;
        Snprintf(pin_info.pin, sizeof(pin_info.pin), query.GetRowData(0));
        pin_info.is_create_by_pm = (ATOI(query.GetRowData(1)) == 0);
        Snprintf(pin_info.account, sizeof(pin_info.account), query.GetRowData(0));
        pin_list.push_back(pin_info);
    }

    return;
}

void CommPerPrivateKey::GetSmartLockOrderedPinInfoList(const std::string& users, UserPinInfoList& pin_list)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn)

    CRldbQuery query(db_conn.get());
    std::stringstream str_sql;
    str_sql << " select K.Code,K.Special,K.Account,P.CredentialID,P.Status,K.UUID,P.SmartLockUUID,PA.UUID From CommPerPrivateKey K"
            << " left join SmartLockPrivateKey P on K.UUID = P.CommPerPrivateKeyUUID"
            << " left join PersonalAccount PA on K.Account = PA.Account"
            << " where K.Account in(" << users << ") order by K.ID;";
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserPinInfo pin_info;
        Snprintf(pin_info.pin, sizeof(pin_info.pin), query.GetRowData(0));
        pin_info.is_create_by_pm = (ATOI(query.GetRowData(1)) == 0);
        Snprintf(pin_info.account, sizeof(pin_info.account), query.GetRowData(2));
        const char* credential_id = query.GetRowData(3);
        pin_info.credential_id = ATOI(credential_id);
        if (strlen(credential_id) == 0)
        {
            pin_info.credential_id = -1;
        }

        pin_info.state = static_cast<SmartLockCredentialState>(ATOI(query.GetRowData(4)));
        Snprintf(pin_info.pin_uuid, sizeof(pin_info.pin_uuid), query.GetRowData(5));
        Snprintf(pin_info.smartlock_uuid, sizeof(pin_info.smartlock_uuid), query.GetRowData(6));
        Snprintf(pin_info.personal_account_uuid, sizeof(pin_info.personal_account_uuid), query.GetRowData(7));
        pin_info.is_support_all_smartlock = true; // 社区目前都下发
        pin_info.project_type = project::PROJECT_TYPE::RESIDENCE;
        pin_list.push_back(pin_info);
    }
}

int CommPerPrivateKey::GetPinInfoByUUID(const std::string& pin_uuid, UserPinInfo& pin_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    std::stringstream str_sql;
    str_sql << "select Code,Special,Account From CommPerPrivateKey where UUID = '" << pin_uuid << "';";
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(pin_info.pin, sizeof(pin_info.pin), query.GetRowData(0));
        pin_info.is_create_by_pm = (ATOI(query.GetRowData(1)) == 0);
        Snprintf(pin_info.account, sizeof(pin_info.account), query.GetRowData(2));
        return 0;
    }

    return -1;
}

}

