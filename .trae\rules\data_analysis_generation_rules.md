DataAnalysis文件生成使用方法：
请根据data_analysis_generation_rules.md中的规则，为以下SQL表生成C++ 新办公（/社区、单住户）DataAnalysis文件：
[你的CREATE TABLE语句]
请生成对应的.h和.cpp文件，需要实现UpdateHandle(/InsertHandle/DeleteHandle)函数，UpdateHandle需关注 'xxxxxxxx，xxxxxxx' 字段。严格按照下面要求。


DataAnalysis代码生成规则

生成代码前，请先读取以下文件了解具体的生成逻辑和实现细节：
- csconfig/src/Model/DataAnalysis/DataAnalysisDevicePushButton.h 和 DataAnalysisDevicePushButton.cpp（模板文件）
- csconfig/src/Model/DataAnalysis/generate_data_analysis.php（生成逻辑）

输入要求：
- 需要给定一个MySQL建表语句
- 建表语句必须包含完整的CREATE TABLE定义，包括字段名、字段类型、主键、索引等
- 需要指定具体的功能需求，例如：
  * 关注哪些字段的变化
  * 变化时投递到单住户、社区还是办公
  * 具体的业务逻辑处理

输出文件生成规则：
1. 头文件(.h)和源文件(.cpp)生成目录根据场景类型确定：
   - 社区/单住户：生成在csconfig/src/Model/DataAnalysis/目录下
   - 办公：生成在csconfig-office/src/Model/DataAnalysis/目录下
2. 文件命名规则：
   - 头文件：DataAnalysis表名.h
   - 源文件：DataAnalysis表名.cpp
4. 头文件保护宏命名规则：
   - 格式：__CSADAPT_DATAANALYSIS_表名大写_H__
   - 表名转换为大写并用下划线分隔
5. 枚举命名规则：
   - 枚举类型名：DA表名Index
   - 枚举值：DA_INDEX_表名大写_字段名大写
6. 函数命名规则：
   - 注册函数：RegDa表名Handler()
   - 处理函数：ItemChangeHandle、InsertHandle、DeleteHandle、UpdateHandle
7. 代码结构要求：
   - 头文件包含必要的include和枚举定义
   - 源文件包含完整的处理函数实现
   - 必须包含注册函数实现
8. 功能实现要求：
   - 根据用户描述实现具体的业务逻辑
   - 在UpdateHandle中检测指定字段的变化
   - 根据业务需求投递到单住户、社区或办公
   - 使用正确的change_type和投递目标

9. 投递场景选择规则：
   - 社区：使用UCCommunityFileUpdate、UCCommunityAccessUpdate、UCCommunityDevUpdate
   - 单住户：使用UCPersonalFileUpdate
   - 新办公：使用UCOfficeFileUpdate、UCOfficeAccessUpdate、UCOfficeDevUpdate
   - 根据用户描述选择合适的投递目标

10. 场景处理范围划分：
    Model目录（csconfig/src/Model/DataAnalysis/）- 社区和单住户
    - 处理社区（project::RESIDENCE）的数据分析
    - 处理单住户（project::PERSONAL）的数据分析
    - 使用UCCommunityFileUpdate、UCCommunityAccessUpdate、UCCommunityDevUpdate、UCPersonalFileUpdate

    DataAnalysis目录（csconfig-office/src/Model/DataAnalysis/）- 新办公
    - 处理新办公（project::OFFICE）的数据分析
    - 使用UCOfficeFileUpdate、UCOfficeAccessUpdate、UCOfficeDevUpdate

11. 选择Update类的指导原则：
    - 如果涉及权限组、用户权限变更 → 使用UCCommunityAccessUpdate/UCOfficeAccessUpdate
    - 如果涉及设备配置、用户信息、联系人等配置文件变更 → 使用UCCommunityFileUpdate/UCPersonalFileUpdate/UCOfficeFileUpdate
    - 如果涉及设备添加/删除的特殊处理（如通知设备上报状态、注销SIP等） → 使用UCCommunityDevUpdate/UCOfficeDevUpdate
    - 大多数情况下使用FileUpdate类，AccessUpdate用于权限相关，DevUpdate用于设备生命周期管理

12. 重要要求：
    - 严格按照模板文件的结构和命名规范
    - 保持代码风格一致
    - 确保所有必要的include都已包含
    - 根据用户的具体需求实现功能，不要只是模板代码
    - 根据业务场景选择合适的Update类，避免错误使用
    - 明确区分社区/单住户和新办公的处理范围 