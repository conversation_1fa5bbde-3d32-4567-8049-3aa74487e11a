#ifndef __CSYUNJIROBOT_HTTP_API_H__
#define __CSYUNJIROBOT_HTTP_API_H__

#include "util_time.h"
#include "json/json.h"
#include "AkLogging.h"
#include "SafeCacheConn.h"
#include "AkcsHttpRequest.h"
#include "YunJiRobotConfig.h"
#include "YunJiRobotApiUtil.hpp"

extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;

class YunJiRobotApi
{
public:
    static bool GetAccessToken(const std::string& access_key_id, const std::string& access_key_secret, YunJiAccessTokenData& token_data)
    {
        // 构造请求URL
        std::string request_url = std::string(g_yunji_robot_config.yunji_http_url) + "/v3/auth/accessToken";

        // 生成认证信息
        YunJiAuthInfo auth_info = YunJiRobotApiUtil::YunJiApiSignature(access_key_id, access_key_secret);

        // 构造JSON请求体
        std::string request_body = YunJiRobotApiUtil::BuildAuthRequestBody(auth_info);

        AK_LOG_INFO << "GetAccessToken request URL: " << request_url;
        AK_LOG_INFO << "GetAccessToken request body: " << request_body;

        // 发送POST请求
        std::string response;
        int result = model::HttpRequest::GetInstance().Post(request_url, request_body, response, 1); // data_type=1 表示JSON

        if (result != 0)
        {
            AK_LOG_ERROR << "GetAccessToken HTTP request failed, result: " << result;
            return false;
        }

        AK_LOG_INFO << "GetAccessToken response: " << response;

        // 解析响应
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response, root))
        {
            AK_LOG_ERROR << "GetAccessToken parse response failed: " << response;
            return false;
        }

        // 检查响应状态
        if (root.isMember("code") && root["code"].asInt() == 0)
        {
            if (root.isMember("data") && root["data"].isObject())
            {
                if (token_data.FromJson(root["data"], access_key_secret))
                {
                    AK_LOG_INFO << "GetAccessToken success, token: " << token_data.access_token;
                    AK_LOG_INFO << "Token expiration: " << token_data.expiration;
                    AK_LOG_INFO << "Store ID: " << token_data.store_id;
                    return true;
                }
                else
                {
                    AK_LOG_ERROR << "GetAccessToken failed to parse token data";
                    return false;
                }
            }
        }

        AK_LOG_ERROR << "GetAccessToken failed, response: " << response;
        return false;
    }

    static bool RequestRobotMerchantCall(const YunJiAccessTokenData& access_token_data,
                                        const MerchantCallRequest& request,
                                        MerchantCallResponse& response_data)
    {
        // 构造请求URL
        std::string request_url = std::string(g_yunji_robot_config.yunji_http_url) + "/v3/rcs/task/create/merchant-call";

        // 构造请求体JSON
        std::string request_body = request.ToJsonString();

        AK_LOG_INFO << "RequestRobotMerchantCall URL: " << request_url;
        AK_LOG_INFO << "RequestRobotMerchantCall request body: " << request_body;

        YunJiAuthInfo auth_info = YunJiRobotApiUtil::YunJiApiSignature(access_token_data.access_key_id, access_token_data.access_key_secret);
    
        std::vector<std::string> headers = YunJiRobotApiUtil::BuildAuthHeaders(auth_info, access_token_data.access_token);

        std::string response;
        int result = model::HttpRequest::GetInstance().PostWithHeaders(request_url, headers, request_body, response, 1); // data_type=1 表示JSON

        if (result != 0)
        {
            AK_LOG_ERROR << "RequestRobotMerchantCall HTTP request failed, result: " << result;
            return false;
        }

        AK_LOG_INFO << "RequestRobotMerchantCall response: " << response;

        // 解析响应
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response, root))
        {
            AK_LOG_ERROR << "RequestRobotMerchantCall parse response failed: " << response;
            return false;
        }

        // 解析响应数据到结构体
        if (response_data.FromJson(root))
        {
            AK_LOG_INFO << "RequestRobotMerchantCall success, taskId: " << response_data.task_id;
            return true;
        }
        else
        {
            AK_LOG_ERROR << "RequestRobotMerchantCall failed, code: " << response_data.code
                        << ", message: " << response_data.message;
            return false;
        }
    }

    static bool RequestRobotMerchantCall(const YunJiAccessTokenData& access_token_data,
                                               const std::string& out_task_id,
                                               const std::string& target,
                                               const std::string& via, 
                                             MerchantCallResponse& response_data)
    {
        MerchantCallRequest request;
        request.store_id = access_token_data.store_id;
        request.out_task_id = out_task_id;
        request.target = target;
        request.via = via;

        std::vector<GoodsItem> goods;
        goods.push_back(GoodsItem("take-out-goods-id-0001", "外卖商品", 1));
        request.goods = goods;

        return RequestRobotMerchantCall(access_token_data, request, response_data);
    }

    static bool RequestCancelRobotTask(const YunJiAccessTokenData& access_token_data,
                                               const std::string& yunji_task_id,
                                               const std::string& robot_id)
    {
        // 构造请求URL - 取消任务API
        std::string request_url = std::string(g_yunji_robot_config.yunji_http_url) + 
                                 "/v3/stores/" + access_token_data.store_id + "/tasks/" + yunji_task_id + "/status";

        // 构造请求体
        CancelRobotTaskRequest request;
        request.new_status = "cancelled";
        if (!robot_id.empty()) {
            request.robot_id = robot_id;
        }
        request.option = "DONT_BACK_DEFAULT_ORIGIN";

        std::string request_body = request.ToJsonString();

        AK_LOG_INFO << "RequestCancelRobotTask URL: " << request_url;
        AK_LOG_INFO << "RequestCancelRobotTask request body: " << request_body;

        YunJiAuthInfo auth_info = YunJiRobotApiUtil::YunJiApiSignature(access_token_data.access_key_id, access_token_data.access_key_secret);
    
        std::vector<std::string> headers = YunJiRobotApiUtil::BuildAuthHeaders(auth_info, access_token_data.access_token);

        std::string response;
        int result = model::HttpRequest::GetInstance().PutWithHeaders(request_url, headers, request_body, response, 1); // data_type=1 表示JSON

        if (result != 0)
        {
            AK_LOG_ERROR << "RequestCancelRobotTask HTTP request failed, result: " << result;
            return false;
        }

        AK_LOG_INFO << "RequestCancelRobotTask response: " << response;

        // 解析响应
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response, root))
        {
            AK_LOG_ERROR << "RequestCancelRobotTask parse response failed: " << response;
            return false;
        }

        // 解析响应数据到结构体
        CancelRobotTaskResponse response_data;
        if (response_data.FromJson(root))
        {
            AK_LOG_INFO << "RequestCancelRobotTask success";
            return true;
        }
        else
        {
            AK_LOG_ERROR << "RequestCancelRobotTask failed, code: " << response_data.code
                        << ", message: " << response_data.message;
            return false;
        }
    }
    
    // 取消排队中的任务
    static bool RequestCancelInQueenRobotTask(const YunJiAccessTokenData& access_token_data,
                                               const std::string& yunji_task_id)
    {
        // 构造请求URL - 取消排队任务API
        std::string request_url = std::string(g_yunji_robot_config.yunji_http_url) + "/v3/rcs/task/up/cancel";

        // 构造请求体
        CancelInQueenRobotTaskRequest request(access_token_data.store_id, yunji_task_id);
        std::string request_body = request.ToJsonString();

        AK_LOG_INFO << "RequestCancelInQueenRobotTask URL: " << request_url;
        AK_LOG_INFO << "RequestCancelInQueenRobotTask request body: " << request_body;

        YunJiAuthInfo auth_info = YunJiRobotApiUtil::YunJiApiSignature(access_token_data.access_key_id, access_token_data.access_key_secret);
    
        std::vector<std::string> headers = YunJiRobotApiUtil::BuildAuthHeaders(auth_info, access_token_data.access_token);

        std::string response;
        int result = model::HttpRequest::GetInstance().PostWithHeaders(request_url, headers, request_body, response, 1); // data_type=1 表示JSON

        if (result != 0)
        {
            AK_LOG_ERROR << "RequestCancelInQueenRobotTask HTTP request failed, result: " << result;
            return false;
        }

        AK_LOG_INFO << "RequestCancelInQueenRobotTask response: " << response;

        // 解析响应
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response, root))
        {
            AK_LOG_ERROR << "RequestCancelInQueenRobotTask parse response failed: " << response;
            return false;
        }

        // 检查响应状态
        if (root.isMember("code") && root["code"].asInt() == 0)
        {
            AK_LOG_INFO << "RequestCancelInQueenRobotTask success";
            return true;
        }
        else
        {
            std::string message = root.isMember("message") ? root["message"].asString() : "Unknown error";
            AK_LOG_ERROR << "RequestCancelInQueenRobotTask failed, code: " << root["code"].asInt()
                        << ", message: " << message;
            return false;
        }
    }

    static bool GetStoreRobotList(const YunJiAccessTokenData& access_token_data, DeviceListResponse& device_list)
    {
        std::string request_url = std::string(g_yunji_robot_config.yunji_http_url) + "/v3/device/list";

        AK_LOG_INFO << "GetStoreRobotList URL: " << request_url;

        YunJiAuthInfo auth_info = YunJiRobotApiUtil::YunJiApiSignature(access_token_data.access_key_id, access_token_data.access_key_secret);
     
        std::string response;
        std::vector<std::string> headers = YunJiRobotApiUtil::BuildAuthHeaders(auth_info, access_token_data.access_token);

        model::HttpRespuestKV parma_kv;
        parma_kv.insert(map<std::string, std::string>::value_type("deviceType", "RSASS"));
        parma_kv.insert(map<std::string, std::string>::value_type("storeId",  access_token_data.store_id));

        int result = model::HttpRequest::GetInstance().GetWithHeaders(request_url, headers, parma_kv, response); 

        if (result != 0)
        {
            AK_LOG_ERROR << "GetStoreRobotList HTTP request failed, result: " << result;
            return false;
        }
        AK_LOG_INFO << "GetStoreRobotList response: " << response;

        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response, root))
        {
            AK_LOG_ERROR << "GetStoreRobotList parse response failed: " << response;
            return false;
        }
        
        if (device_list.FromJson(root))
        {
            AK_LOG_INFO << "GetStoreRobotList success, found " << device_list.devices.size() << " devices";
            return true;
        }
        else
        {
            AK_LOG_ERROR << "GetStoreRobotList failed, code: " << device_list.code
                        << ", message: " << device_list.message;
            return false;
        }
    }

    // 获取排队任务
    static bool GetPendingTaskList(const YunJiAccessTokenData& access_token_data, TaskListResponse& task_list)
    {
        std::string request_url = std::string(g_yunji_robot_config.yunji_http_url) + "/v3/rcs/task/pending-task/list";

        AK_LOG_INFO << "GetPendingTaskList URL: " << request_url;

        YunJiAuthInfo auth_info = YunJiRobotApiUtil::YunJiApiSignature(access_token_data.access_key_id, access_token_data.access_key_secret);

        std::string response;
        std::vector<std::string> headers = YunJiRobotApiUtil::BuildAuthHeaders(auth_info, access_token_data.access_token);

        model::HttpRespuestKV parma_kv;
        parma_kv.insert(map<std::string, std::string>::value_type("storeId",  access_token_data.store_id));

        int result = model::HttpRequest::GetInstance().GetWithHeaders(request_url, headers, parma_kv, response);

        if (result != 0)
        {
            AK_LOG_ERROR << "GetPendingTaskList HTTP request failed, result: " << result;
            return false;
        }
        AK_LOG_INFO << "GetPendingTaskList response: " << response;

        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response, root))
        {
            AK_LOG_ERROR << "GetPendingTaskList parse response failed: " << response;
            return false;
        }

        if (task_list.FromJson(root))
        {
            AK_LOG_INFO << "GetPendingTaskList success, found " << task_list.tasks.size() << " tasks";
            return true;
        }
        else
        {
            AK_LOG_ERROR << "GetPendingTaskList failed, code: " << task_list.code
                        << ", message: " << task_list.message;
            return false;
        }
    }

    // 便捷方法：直接返回设备列表
    static std::vector<DeviceInfo> GetStoreRobotList(const YunJiAccessTokenData& access_token_data)
    {
        DeviceListResponse device_list;
        bool success = GetStoreRobotList(access_token_data, device_list);
        
        if (success)
        {
            return device_list.devices;
        }
        
        return std::vector<DeviceInfo>(); // 返回空列表
    }

};

#endif
