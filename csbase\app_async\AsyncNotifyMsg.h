#ifndef _ASYNC_NOTIFY_MSG_H_
#define _ASYNC_NOTIFY_MSG_H_

#include <string>
#include "json/json.h"
#include "AkcsMsgDef.h"

class AsyncNotifyMsg
{
public:
    AsyncNotifyMsg();
    virtual ~AsyncNotifyMsg();
    virtual const std::string&  GetMsgType() = 0;
    
    std::string BaseBuildAsyncMsg(const Json::Value& json_datas)
    {
        Json::Value data;
        data["err_code"] = APP_ERR_CODE_SUCCESS;
        data["message"] = "success";
        data["datas"] = json_datas;

        Json::FastWriter writer;
        std::string msg = writer.write(data);
        return std::move(msg);        
    }

};

#endif // _ASYNC_NOTIFY_MSG_H_
