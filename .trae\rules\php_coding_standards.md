PHP编程规范

0 概要

本规范针对应用后台PHP开发必须遵守的编程规范，主要包括命名规范、代码格式规范等内容。

1 命名规范

    1 变量命名规范

    1.1 普通变量
    普通变量命名遵循以下规则：
    - 以小写开头的驼峰法命名
    - 示例：`$myName`、`$userName`、`$maxCount`

    1.2 静态变量
    静态变量命名遵循以下规则：
    - 静态变量使用小写的s开头
    - 以小写开头的驼峰法命名
    - 示例：`$sBaseDir`、`$sInstance`、`$sCounter`

    1.3 全局变量
    全局变量应该带前缀'g'，命名遵循以下规则：
    - 全局变量使用小写的g开头
    - 以小写开头的驼峰法命名
    - 示例：
    ```php
    global $gApp;
    global $gConfig;
    global $gLogger;
    ```

    1.4 const常量
    - 以全大写命名，多个单词之间以下划线分隔
    - 示例：`SYSTEM_CONFIG`、`MAX_BUFFER_SIZE`、`DEBUG_MODE`

2 类和方法命名规范

    2.1 类命名
    - 以大写开头的驼峰法命名
    - 示例：`class GetUserInAccessGroup`、`class UserManager`、`class ConfigParser`

    2.2 方法或函数命名
    - 以小写开头的驼峰法命名
    - 示例：`function addUser()`、`function getUserInfo()`、`function processData()`

3 文件命名规范

    3.1 文件命名
    - 使用小驼峰命名，文件名称与类名尽量保持一致（文件名首字母小写，类名为大写）
    - 示例：`personalAccount.php`、`userManager.php`、`configParser.php`

4 命名空间规范

    4.1 命名空间
    - 使用命名空间，避免方法命名冲突
    - 可以参考文件夹路径/文件名等层级关系命名
    - 示例：
    ```php
    namespace util\response;
    namespace model\user;
    namespace service\auth;
    ```

代码格式规范

    1 缩进和空格
    1.1 缩进
    - 使用4个空格进行缩进，不使用Tab
    - 保持一致的缩进风格

    1.2 空格使用
    - 运算符前后加空格：`$a + $b`
    - 逗号后加空格：`function($a, $b, $c)`
    - 数组元素间加空格：`array($a, $b, $c)`

    2 大括号风格

    2.1 K&R风格
    - 左大括号换行
    - 右大括号单独占一行
    - 示例：
    ```php
    if ($condition)
    {
        // 代码块
    }
    else
    {
        // 代码块
    }
    ```
