C++编程规范

0 概要

本规范基于《LINUX服务器C/C++编码规范》《应用后台业务开发规范》重新整理，针对当前后台C++开发必须遵守的规范。除了编程规范，也新增了我们使用的一些规范。可以在代码书写完成之后调用AI检查目前修改的代码是否符合我们的代码规范。

1 使用规范
1. 禁止使用strcpy、atoi等不安全函数，用封装的Snprintf、ATOI代替
2. 所有字符串操作必须进行边界检查
3. 使用nullptr代替NULL
4. 使用static_cast进行显式转换，避免使用C风格转换
5. 避免危险的隐式转换，使用explicit关键字
6. 使用glog日志级别：INFO（一般信息）、WARNING（警告信息）、ERROR（错误信息）、FATAL（致命错误）
7. 消息ID使用有意义的命名，避免使用魔法数字，记录ID的含义
8. csoffice组件不能调用住宅的ResidentPerAccount结构体，统一使用OfficeAccount
9. 数据库操作规范
    9.1 数据库连接获取接口(RAII)
        使用方式：
        ```cpp
        GET_DB_CONN_ERR_RETURN(db_conn, -1);
        CRldbQuery query(db_conn.get());
        ```
        不推荐使用：
        ```cpp
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* tmp_conn = conn.get();
        if (NULL == tmp_conn)
        {
        AK_LOG_ERROR << "Get DB conn failed.";
        return -1;
        }
        ```
    9.2 数据库插入数据接口
        使用规范：
        - 插入数据时使用std::map<std::string, std::string>类型的strMap
        - 使用std::map<std::string, int>类型的intMap处理整型数据
        示例代码：
        ```cpp
        // 插入数据构造
        std::map<std::string, std::string> strMap;
        strMap.emplace("MAC", personal_act_log.mac);
        strMap.emplace("PicName", personal_act_log.pic_name);

        std::map<std::string, int> intMap;
        intMap.emplace("Response", personal_act_log.resp);
        intMap.emplace("CaptureType", personal_act_log.act_type);
        // 表名构造
        std::string table_name = "xxxxxxxxxx";
        // 数据插入
        int ret = tmp_conn->InsertData(table_name, strMap, intMap);
        ```
    9.3 一个线程只能获取一个数据库连接，避免多连接导致的资源竞争和死锁问题
    9.4 C++数据库NULL字段处理时，插入数据前必须检查字段是否为空，避免插入NULL值导致数据异常
    9.5 C++ sql封装对NULL的判断
        使用方式：
        ```cpp
        std::stringstream strSQL;
        strSQL << "SELECT  ISNULL(featureexpiretime) From xxxxxxxxx where xxxxx";

10. C++ Json解析规范
    10.1 对外约定的协议，json中的最终变量统一使用int、string，不使用uint、bool等类型
    10.2 对于int的字段解析，统一使用asInt() - 若客户端传错类型（如恶意攻击），此函数会按默认值处理
    10.3 对于string的字段解析，统一使用asString() - 若客户端传错类型（如恶意攻击），此函数会按默认值处理（如果传的是数字类型的，会转换成string）
    10.4 对于json中的某个字段为object/array类型的，要先判断合法性，再使用，举例如下：
        ```json
        {
            "id": "12345678",
            "command": "v1.0_report_lock_event",
            "param": {
                "event_type": "battery_level",
                "event_data": {
                    "battery_level": 88
                }
            }
        }
        ```
        要获取battery_level变量，不能直接root["param"]["event_data"]["battery_level"]获取
        而需要：
        ```cpp
        if(isMemberCheckType("param", Akcs::Json::objectValue))
        {
            Json::Value param = root["param"];
            if(isMemberCheckType("event_data", Akcs::Json::objectValue))
            {
                Json::Value event_data = param["event_data"];
                int battery_level = event_data["battery_level"].asInt();
            }
        }
        ```
11. redis使用规范
    11.1 Redis连接使用SafeCacheConn进行RAII管理，避免手动获取CacheManager实例
        使用方式：
        ```cpp
        SafeCacheConn cache_conn(g_redis_db_userdetail);
        if (cache_conn.isConnect())
        {
            //csmain插入，防止处理不过来，设备一直重复请求
            cache_conn.del(msg.accounts_key());
        }
        ```
        不使用：
        ```cpp
        CacheManager* cache_mng = CacheManager::getInstance();
        CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail);
        if (cache_conn)
        {
            //csmain插入，防止处理不过来，设备一直重复请求
            cache_conn->del(msg.accounts_key());
            cache_mng->RelCacheConn(cache_conn);
        }
        ```
    11.2 合理使用Redis缓存，设置适当的过期时间，处理缓存失效

2 命名规范
    1 变量命名规范
    1.1 普通变量
        - 使用小写字母开头，多个单词用驼峰命名
        - 示例：userName、maxCount、isValid

    1.2 静态变量
        - 以s_开头，表示static
        - 示例：s_instance、s_counter

    1.3 类成员变量
        - 以_结束
        - 示例：count_、name_

    1.4 全局变量
        - 以g_开头，表示global
        - 尽量避免使用全局变量，必须使用时，使用extern声明
        - 示例：g_config、g_logger

    2 函数命名规范
    2.1 普通函数
        - 使用小驼峰命名法，动词开头，表示动作
        - 示例：getUserInfo、setConfig、processData

    2.2 布尔函数
        - 使用is、has、can等开头
        - 示例：isValid、hasPermission、canAccess

    2.3 类成员函数
        - 以小驼峰命名法，动词开头
        - 示例：getUserInfo、setConfig、processData

    3 类命名规范
    3.1 类名
        - 使用大驼峰命名法
        - 示例：UserManager、ConfigParser

    3.2 接口类
        - 以I开头，表示Interface
        - 示例：IUserService、IDataProcessor

    4 文件命名规范
    4.1 头文件
        - 使用驼峰命名法（PascalCase），首字母大写
        - 示例：UserManager.h、ConfigParser.h、AkcsWebPduBase.h

    4.2 源文件
        - 与头文件对应，使用.cpp扩展名
        - 示例：UserManager.cpp、ConfigParser.cpp、AkcsWebPduBase.cpp

    5 枚举命名规范
    5.1 枚举类型名
        - 使用大驼峰命名法
        - 示例：UserStatus、ConfigType

    5.2 枚举值
        - 使用全大写字母，单词间用下划线分隔
        - 示例：USER_ACTIVE、CONFIG_DEFAULT

    6 常量和宏定义规范
    6.1 常量定义
        - 使用const关键字定义常量
        - 全局常量定义在头文件中
        - 使用全大写加下划线命名
        - 示例：const int MAX_BUFFER_SIZE = 1024;

    6.2 宏定义
        - 使用全大写字母，单词间用下划线分隔
        - 示例：MAX_BUFFER_SIZE、DEBUG_MODE

    6.3 条件编译宏
        - 以项目名开头
        - 示例：AKCS_DEBUG、AKCS_VERSION

3 代码格式规范

    1 缩进和空格
    1.1 缩进
        - 使用4个空格进行缩进，不使用Tab
        - 保持一致的缩进风格
    1.2 空格使用
        - 运算符前后加空格：a + b
        - 逗号后加空格：func(a, b, c)

    2 大括号风格
    2.1 K&R风格
        - 左大括号换行
        - 右大括号单独占一行
        - 示例：
        ```cpp
        if (condition)
        {
            // 代码块
        }
        else
        {
            // 代码块
        }
        ```
        错误格式：
        - 避免：if (condition) { }
        - 避免：if (condition) xxxxx;

    3 行长度和换行
    3.1 最大行长度
        - 不超过120个字符
        - 超过时进行换行
    3.2 换行规则
        - 在运算符处换行
        - 函数参数过多时换行
        - 保持适当的缩进

    4 函数长度
        4.1 函数大小
        - 函数不超过50行
        - 超过时考虑拆分

    5 局部变量
        5.1 声明位置
        - 在使用前最近的位置声明
        - 避免在函数开头声明所有变量
        
        5.2 初始化
        - 声明时立即初始化
        - 示例：int count = 0;

    6 作用域
        6.1 最小作用域原则
        - 变量在最小作用域内声明
        - 避免不必要的变量作用域扩大








