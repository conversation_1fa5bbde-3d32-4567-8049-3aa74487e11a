#include "handle_door_log.h"
#include <sstream>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "DeviceSetting.h"
#include "doorlog/UserInfo.h"
#include "PersonalAccount.h"
#include "personal_capture.h"
//#include "RecordActLog.h"
#include "AkcsCommonDef.h"
//#include "RecordOfficeLog.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"
#include "doorlog/RecordNewOfficeLog.h"
#include "AkcsCommonSt.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/ProjectInfo.h"
#include "storage_mq.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/OfficeMessage.h"
#include "dbinterface/Log/ParkingLog.h"
#include "doorlog/RecordParkingLog.h"
#include "AkcsCommonSt.h"
#include "dbinterface/AntiPassbackDoor.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

CHandleDoorLog& CHandleDoorLog::GetInstance()
{
    static CHandleDoorLog handle_door_log;
    return handle_door_log;
}

//根据MAC获取设备设置信息
static int GetDeviceSettingByMac(const std::string &mac, ResidentDev &dev)
{
    if (dbinterface::ResidentDevices::GetMacDev(mac, dev) == 0)
    {
        return 0;
    }
    else if (dbinterface::ResidentPerDevices::GetMacDev(mac, dev) == 0)
    {
        return 0;
    }

    return -1;//查询到空值
}


int CHandleDoorLog::InsertCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg)
{
    ResidentDev dev;
    int ret = GetDeviceSettingByMac(mac, dev);
    if (ret != 0)
    {
        AK_LOG_WARN << "MAC=" << mac << " Not Found Devices Info.";
        return -1;
    }

    if (RecordActLog::GetInstance().RewriteProjectInfo(act_msg, dev) != 0 ) 
    {
        AK_LOG_WARN << "RewriteProjectInfo error mac:" << mac;
        return -1;
    } 


    act_msg.grade = dev.grade;
    act_msg.unit_id = dev.unit_id;
    act_msg.mng_type = dev.is_personal;
    act_msg.is_public = dev.is_public;
    act_msg.unit_id = dev.unit_id;
    Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account),  dev.sip);
    Snprintf(act_msg.location, sizeof(act_msg.location),  dev.location);
    //个人查询时候 是根据mac或node属于它的过滤。那么对于公共设备的开门，应该记录对应开门人的node, 而对于物业mac就可以过滤出记录
    Snprintf(act_msg.account, sizeof(act_msg.account),  dev.node);//node
    // if (strlen(act_msg.initiator) == 0)
    // {
    //     AK_LOG_WARN << "parameter error,Initiator is null";
    //     return -1;
    // }
    Snprintf(act_msg.mac, sizeof(act_msg.mac),  dev.mac);
    Snprintf(act_msg.dev_uuid, sizeof(act_msg.dev_uuid),  dev.uuid);
    Snprintf(act_msg.key, sizeof(act_msg.key),  act_msg.initiator);//所用的key
    Snprintf(act_msg.unit_uuid, sizeof(act_msg.unit_uuid),  dev.unit_uuid);

    //此处为了兼容535设备上报指纹开门字段为6，在此处将解析出来type为6时强行转换成18
    if (act_msg.act_type == 6)
    {
        act_msg.act_type = ACT_OPEN_DOOR_TYPE::FINGERPRINT_UNLOCK; 
    }

    if (dev.conn_type == csmain::DeviceType::OFFICE_DEV)
    {
        OfficeInfo office_info(act_msg.project_uuid2);
        if (office_info.IsNew())
        {
            return InsertNewOfficeCapture(mac, act_msg, dev);
        }
        return InsertOfficeCapture(mac, act_msg, dev);
    }

    return InsertResidentCapture(mac, act_msg, dev);
}

int CHandleDoorLog::InsertOfficeCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev &dev)
{
    AK_LOG_INFO << dev.mac << " office device report activity logs. type:" << act_msg.act_type << " initiator:" << act_msg.initiator;
    if (RecordActLog::GetInstance().IsUserActType(act_msg.act_type))
    {
        if (strlen(act_msg.per_id) < 2)
        {
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  "visitor");
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
            RecordActLog::GetInstance().SetCaptureAction(act_msg);
        }
        else
        {
            RecordOfficeLog::GetInstance().OfficeModeHandle(act_msg);
        }
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CALL)
    {
        RecordOfficeLog::GetInstance().RecordOfficeCallLog(act_msg);
    }
    else if ((act_msg.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY))
    {
        PersonalTempKeyUserInfo tempkey_user_info;
        RecordOfficeLog::GetInstance().RecordOfficeTmpKeyLog(act_msg, tempkey_user_info);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR)
    {
        RecordOfficeLog::GetInstance().RecordOfficeRemoteLog(act_msg);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::TEMP_CAPTURE)  //测温上报
    {
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  act_msg.initiator);
        if (dbinterface::PersonalCapture::AddTemperatureCapture(act_msg) < 0)
        {
            AK_LOG_WARN << "Add  temperature capture failed.";
            return -1;
        }
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)
    {
        // 内开门 Exit Button类型:Initiated By显示--，Key显示--，Company显示--
        Snprintf(act_msg.key, sizeof(act_msg.key), "--");
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "--");
    }
    else
    {
        AK_LOG_WARN << "invalid open door active type: " << act_msg.act_type;
        return -1;
    }

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }

    return 0;
}


int CHandleDoorLog::InsertNewOfficeCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev &dev)
{
    AK_LOG_INFO << dev.mac << " new office device report activity logs. type:" << act_msg.act_type << " initiator:" << act_msg.initiator;
    if (RecordActLog::GetInstance().IsUserActType(act_msg.act_type))
    {
        if (strlen(act_msg.per_id) < 2)
        {
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  "visitor");
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
            RecordActLog::GetInstance().SetCaptureAction(act_msg);
        }
        else
        {
            RecordNewOfficeLog::GetInstance().NewOfficeModeHandle(act_msg);
        }
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CALL)
    {
        RecordNewOfficeLog::GetInstance().RecordNewOfficeCallLog(act_msg);
    }
    else if ((act_msg.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY))
    {
        OfficeTempKeyInfo tempkey_user_info;
        RecordNewOfficeLog::GetInstance().RecordNewOfficeTmpKeyLog(act_msg, tempkey_user_info);
         std::string reciver = tempkey_user_info.creator_personal_account_uuid;
        if (act_msg.resp || (reciver.size() == 0))
        {
            AK_LOG_WARN << "InsertNewOfficeCapture failed: mac=" << act_msg.mac
                        << ", resp=" << act_msg.resp << ", reciver=" << reciver;
            return -1;
        }
        std::string message_uuid;
        std::string rbac_uuid = tempkey_user_info.rbac_datagroup_uuid;

        // insert tempkey used notify to database.
        if (dbinterface::OfficeMessage::InsertOfficeMessageAndReciver(reciver, tempkey_user_info.name, rbac_uuid, message_uuid) != 0)
        {
            AK_LOG_WARN << "InsertNewOfficeCapture failed: reciver=" << reciver
                << ", name=" << tempkey_user_info.name << ", message_uuid=" << message_uuid;
            return -1;
        }
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR)
    {
        RecordNewOfficeLog::GetInstance().RecordNewOfficeRemoteLog(act_msg);
    }
    else if (RecordActLog::GetInstance().EmergencyType(act_msg))
    {
        Snprintf(act_msg.key, sizeof(act_msg.key), "--");
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.initiator);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)
    {
        // 内开门 Exit Button类型:Initiated By显示--，Key显示--，Company显示--
        Snprintf(act_msg.key, sizeof(act_msg.key), "--");
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "--");
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::HTTP_UNLOCK)
    {
        RecordNewOfficeLog::GetInstance().RecordOfficeLocalHttpLog(act_msg);
    }
    else
    {
        AK_LOG_WARN << "invalid open door active type: " << act_msg.act_type;
        return -1;
    }
    
    if (strlen(act_msg.account) > 0 && RecordActLog::GetInstance().IsUserAttendanceActType(act_msg.act_type)) {
        DatabaseExistenceStatus is_attendance = dbinterface::OfficeDevices::GetIsAttendanceByUUID(dev.uuid);
        if (DatabaseExistenceStatus::EXIST == is_attendance ||
            DatabaseExistenceStatus::QUERY_ERROR == is_attendance) {
            Json::Value item;
            Json::FastWriter writer;
            item["device_uuid"] = dev.uuid;
            item["clock_time"] = (Json::Value::UInt)act_msg.capture_time;
            item["relay"] = DoornumToRelayStatus(act_msg.relay);
            item["security_relay"] = DoornumToRelayStatus(act_msg.srelay);
            item["account"] = act_msg.account;
            std::string data_json = writer.write(item);
            SendWebCommonMsg(PUSH_WEB_MSG_ATTENDANCE, data_json);
        }
    }

    // 通知web
    NotifyWebAccessDoorMsg(act_msg);

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }
    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK && act_msg.mng_type == 0 && act_msg.resp == CAPTURE_LOG_RET_TYPE::SUCCESS) {
        PARKING_LOG parking_log;
        RecordParkingLog::GetInstance().NewParkingHandle(act_msg, parking_log, dev);
        RecordParkingLog::GetInstance().RecordParkingVehicleLog(parking_log);
    }

    return 0;
}

void CHandleDoorLog::NotifyWebAccessDoorMsg(const SOCKET_MSG_DEV_REPORT_ACTIVITY& activity)
{
    //开门成功且有进出模式，才通知web
    if (activity.resp == 0 && activity.access_mode != (int)AntiPassbackDoorType::NORMAL)
    {
        AccessDoorNotifyMsg access_door_notify_msg;
        GenerateAccessDoorMsg(access_door_notify_msg, activity);
        SendAccessDoorNotifyWebMsg(access_door_notify_msg); 
    }
}

int CHandleDoorLog::GenerateAccessDoorMsg(AccessDoorNotifyMsg& access_door_notify_msg, const SOCKET_MSG_DEV_REPORT_ACTIVITY& activity)
{
    if (activity.per_id[0] == MODE_DELIVERY)
    {
        int delivery_id = ATOI(&activity.per_id[1]);
        OfficeDeliveryInfo delivery_info;
        if (0 != dbinterface::OfficeDelivery::GetOfficeDeliveryByID(delivery_id, delivery_info))
        {
            return -1;
        } 
        Snprintf(access_door_notify_msg.account_type, sizeof(access_door_notify_msg.account_type), "delivery");
        Snprintf(access_door_notify_msg.user_uuid, sizeof(access_door_notify_msg.user_uuid), delivery_info.uuid);
    }
    //能够找到对应用户开门的，才通知web
    else if (strlen(activity.account) > 0)
    {
        std::string per_uuid;
        if (0 != dbinterface::OfficePersonalAccount::GetUUIDByAccount(activity.account, per_uuid))
        {
            return -1;
        }
        //当前只需要上报personnel的
        OfficePersonnelInfo personnel_info;
        if (0 != dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(per_uuid, personnel_info))
        {
            return -1;
        }

        Snprintf(access_door_notify_msg.account_type, sizeof(access_door_notify_msg.account_type), "personnel");
        Snprintf(access_door_notify_msg.user_uuid, sizeof(access_door_notify_msg.user_uuid), personnel_info.uuid);
    }
    else
    {
        return -1;
    }

    std::string access_mode = GetEntryExitModeByAccessMode(activity);
    std::string door_uuid_list = dbinterface::DevicesDoorList::GetDoorUUIDListByRelayList(activity.dev_uuid, activity.relay, activity.srelay);

    if (access_mode.size() == 0 || door_uuid_list.size() == 0)
    {
        return -1;
    }

    Snprintf(access_door_notify_msg.access_mode, sizeof(access_door_notify_msg.access_mode), access_mode.c_str());
    Snprintf(access_door_notify_msg.door_uuid_list, sizeof(access_door_notify_msg.door_uuid_list), door_uuid_list.c_str());
    access_door_notify_msg.timestamp = activity.capture_time;

    return 0;
}

std::string CHandleDoorLog::GetEntryExitModeByAccessMode(const SOCKET_MSG_DEV_REPORT_ACTIVITY& activity)
{
    if (activity.access_mode == (int)AntiPassbackAccessMode::ENTRY || activity.access_mode == (int)AntiPassbackAccessMode::ENTRY_VIOLATION)
    {
        return "entry";
    }
    else if (activity.access_mode == (int)AntiPassbackAccessMode::EXIT || activity.access_mode == (int)AntiPassbackAccessMode::EXIT_VIOLATION)
    {   
        return "exit";
    }
    return "";
}


void CHandleDoorLog::SendAccessDoorNotifyWebMsg(const AccessDoorNotifyMsg& access_door_notify_msg)
{
    Json::Value item;
    Json::FastWriter writer;
    item["account_type"] = access_door_notify_msg.account_type;
    item["mode"] = access_door_notify_msg.access_mode;
    item["user_uuid"] = access_door_notify_msg.user_uuid;
    item["door_uuid"] = access_door_notify_msg.door_uuid_list;
    item["timestamp"] = access_door_notify_msg.timestamp;
    item["message_id"] = (int)NotifyWebAccessDoorMessageID::ENTRY_OR_EXIT_RECORD;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "SendAccessDoorNotifyWebMsg: " << data_json;
    SendWebCommonMsg(PUSH_WEB_MSG_ACCESS_DOOR, data_json);
}


int CHandleDoorLog::InsertResidentCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev &dev)
{
    int handle_mode = RecordActLog::GetInstance().HandleMode(act_msg, dev);
    AK_LOG_INFO << dev.mac << " device report activity logs. type:" << act_msg.act_type 
        << " initiator:" << act_msg.initiator << " handle_mode:" << handle_mode;

    if (handle_mode == NEW_COMMUNITY_NEW_DEVICE)
    {
        if (strlen(act_msg.per_id) < 2)
        {
            //自动开门及快递开门类型
            if(RecordActLog::GetInstance().EmergencyType(act_msg) || act_msg.act_type == ACT_OPEN_DOOR_TYPE::DELIVERY_UNLOCK)
            {
                Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.initiator);
                Snprintf(act_msg.key, sizeof(act_msg.key), "--");
            }
            else
            {
                Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");
            }
            RecordActLog::GetInstance().SetCaptureAction(act_msg);
        }
        else
        {
            RecordActLog::GetInstance().NewModeHandle(act_msg);
        }
        // 三方锁离线不考虑

        if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
        {
            AK_LOG_WARN << "Add personnal motion capture failed.";
            return -1;
        }
        return 0;
    }
    else if (handle_mode == NEW_COMMUNITY_OLD_DEVICE)
    {
        RecordActLog::GetInstance().NewCommunityOldDeviceHandle(act_msg, dev);
        if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
        {
            AK_LOG_WARN << "Add personnal motion capture failed.";
            return -1;
        }

        return 0;
    }
    else
    {
        //OLD_MODE
    }

    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CALL)
    {
        RecordActLog::GetInstance().RecordCallLog(act_msg, dev);
    }
    else if ((act_msg.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY) || (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY))
    {
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");
        RecordActLog::GetInstance().RecordKeyLog(act_msg, dev);
        // tempKey离线也是开门失败，无需通知
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::RFCARD)
    {
        RecordActLog::GetInstance().RecordRfCardLog(act_msg, dev);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::FACE)
    {
        RecordActLog::GetInstance().RecordFaceLog(act_msg, dev);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR)
    {
        RecordActLog::GetInstance().RecordRemoteLog(act_msg);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::TEMP_CAPTURE)  //测温上报
    {
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  act_msg.initiator);
        if (dbinterface::PersonalCapture::AddTemperatureCapture(act_msg) < 0)
        {
            AK_LOG_WARN << "Add  temperature capture failed.";
            return -1;
        }
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::HANDSET_UNLOCK)
    {
        RecordActLog::GetInstance().RecordHandsetLog(act_msg);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)
    {
        // 内开门 Exit Button类型:Initiated By显示--，Key显示--，Company显示--
        Snprintf(act_msg.key, sizeof(act_msg.key), "--");
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "--");
    }
    else
    {
        // 家居内开门也直接忽略
        AK_LOG_WARN << "invalid open door active type: " << act_msg.act_type;
        return -1;
    }

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }
    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK && act_msg.mng_type == 0) {
        PARKING_LOG parking_log;
        RecordParkingLog::GetInstance().NewParkingHandle(act_msg, parking_log, dev);
        RecordParkingLog::GetInstance().RecordParkingVehicleLog(parking_log);
    }
    return 0;
}




