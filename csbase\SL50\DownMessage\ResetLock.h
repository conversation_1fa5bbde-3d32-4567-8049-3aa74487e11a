#ifndef __DOWN_RESET_LOCK_H_
#define __DOWN_RESET_LOCK_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

class ResetLock : public BaseParam {
public:
    static constexpr const char* COMMOND = "v1.0_d_reset";
    static constexpr const char* AKCS_COMMAND = "v1.0_d_reset";

    // 业务参数
    std::string device_id_;

    ResetLock(const std::string& device);

    std::string to_json();
    void from_json(const std::string& json_str);
};
#endif