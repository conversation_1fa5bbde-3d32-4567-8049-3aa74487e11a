数据库文件生成使用方法：
请根据database_interface_interface_generation_rules.md中的规则，为以下SQL表生成C++数据库接口文件：
[你的CREATE TABLE语句]
请生成对应的.h和.cpp文件，以及生成根据 'xxxxx'字段进行查询的函数。严格按照下面要求。


注意：生成代码后，请确认Email、Fax、Name、Phone等敏感字段是否需要加密处理，并根据实际需求调整加密逻辑。

数据库接口代码生成规则

生成代码前，请先读取以下文件了解具体的生成逻辑和实现细节：
- csbase/dbinterface/CustomerService.h 和 CustomerService.cpp（模板文件）
- csbase/dbinterface/generate.php（生成逻辑）

输入要求：
- 需要给定一个MySQL建表语句
- 建表语句必须包含完整的CREATE TABLE定义，包括字段名、字段类型、主键、索引等
- 需要指定要生成的查询函数，格式：Get表名By字段名

输出文件生成规则：
1. 头文件(.h)生成在csbase/dbinterface/目录下
2. 源文件(.cpp)生成在csbase/dbinterface/目录下
3. 排除字段：CreateTime、UpdateTime、ID、Version、包含RBAC的字段
4. 字段类型映射：
   - int类型字段：int field_name;
   - char(n)类型字段：char field_name[n];
   - time类型字段：char field_name[32];
5. 命名转换规则：
   - 驼峰命名转下划线：MngAccount -> mng_account
   - 特殊处理：UUID -> uuid，ID -> id
6. 查询函数生成规则：
   - 必须生成Get表名FromSql函数（用于解析查询结果）
   - 根据指定的查询需求生成对应的Get表名By字段名函数
   - 不生成所有可能的查询函数，只生成需要的
   - 重要：必须严格按照用户指定的查询需求生成，不要根据键约束，生成不需要查询函数
7. 加密字段处理规则：
   - 加密字段：Email、Fax、Name、Phone
   - 读取时使用解密：dbinterface::DataConfusion::Decrypt(query.GetRowData(index)).c_str()
   - 写入时使用加密：dbinterface::DataConfusion::Encrypt(value.c_str())
   - 示例：Snprintf(account.email, sizeof(account.email), dbinterface::DataConfusion::Decrypt(query.GetRowData(2)).c_str());
   - 重要：如果表中有Email、Fax、Name、Phone字段，必须使用加密处理，不要使用普通的query.GetRowData()
8. 重要要求：
   - Snprintf函数必须包含格式字符串"%s"
   - GET_DB_CONN_ERR_RETURN宏必须包含参数
   - 使用固定索引值0,1,2,3,4...而不是index++


