#ifndef _ASYNC_LOCK_STATUS_MSG_H_
#define _ASYNC_LOCK_STATUS_MSG_H_

#include "DclientMsgSt.h"
#include "AK.Server.pb.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "AsyncNotifyMsg.h"
#include "string.h"

class AsyncLockStatusMsg: public AsyncNotifyMsg
{
public:
    AsyncLockStatusMsg() {}
    ~AsyncLockStatusMsg() = default;

    void SetLockUuid(const std::string& lock_uuid) { lock_uuid_ = lock_uuid; }
    void SetStatus(int status) { status_ = status; }
    
    int BuildRouteP2PMsg(uint64_t trace_id, AK::Server::P2PAppAsyncMsg& p2p_msg);

    std::string BuildAsyncMsg();
    const std::string& GetMsgType() override { return msg_type_; }
private:
    std::string msg_type_ = "lock_status";
    std::string lock_uuid_;
    int status_ = 0;

};

#endif // _ASYNC_LOCK_STATUS_MSG_H_
