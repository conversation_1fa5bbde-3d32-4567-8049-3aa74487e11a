#ifndef __PARSE_REPORT_ZIGBEE_SUMMARY_MSG_H__
#define __PARSE_REPORT_ZIGBEE_SUMMARY_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{

/*
<Msg>
<Type>ReportZigbeeSummary</Type>
<Params>
<TotalSlice></TotalSlice>//总共多少个分片
<CurrentSlice></CurrentSlice> //当前是第几个分片
<TraceID></TraceID> //32bit，当前消息上报的Traceid，每个分片的Traceid一致  
<Device>
    <ZigbeeDeviceID>14520404</ZigbeeDeviceID>   
    <Version>1758745320 </Version>
</Device>
<Device>
    <ZigbeeDeviceID>245544524</ZigbeeDeviceID>
    <Version>1758745320 </Version>
</Device>
</Params>
</Msg>
*/

// 使用全局ZigbeeSliceInfo定义
static int ParseReportZigbeeSummaryMsg(char *buf, ZigbeeSliceInfo& slice_info)
{
    if (buf == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportZigbeeSummaryMsg text: \n" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (nullptr == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    // 主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "Mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* params_node = root_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM);
    if (nullptr == params_node)
    {
        AK_LOG_WARN << "Params Node is NULL";
        return -1;
    }

    // 解析分片信息
    TiXmlElement* total_slice_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_TOTAL_SLICE);
    if (total_slice_node && total_slice_node->GetText())
    {
        slice_info.total_slice = ATOI(total_slice_node->GetText());
    }
    
    TiXmlElement* current_slice_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_CURRENT_SLICE);
    if (current_slice_node && current_slice_node->GetText())
    {
        slice_info.current_slice = ATOI(current_slice_node->GetText());
    }
    
    TiXmlElement* trace_id_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_TRACE_ID);
    if (trace_id_node && trace_id_node->GetText())
    {
        slice_info.trace_id = trace_id_node->GetText();
    }

    // 解析设备列表
    TiXmlElement* device_node = nullptr;
    for (device_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_DEVICE); device_node; device_node = device_node->NextSiblingElement(XML_NODE_NAME_MSG_PARAM_DEVICE))
    {
        SOCKET_MSG_ZIGBEE_DEVICE_SUMMARY zigbee_device;
        memset(&zigbee_device, 0, sizeof(zigbee_device));
        
        TiXmlElement* sub_node = nullptr;
        for (sub_node = device_node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
        {
            if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ZIGBEE_DEVICE_ID) == 0)
            {
                TransUtf8ToTchar(sub_node->GetText(), zigbee_device.zigbee_device_id, sizeof(zigbee_device.zigbee_device_id) / sizeof(TCHAR));
            }
            else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_COMMON_VERSION) == 0)
            {
                TransUtf8ToTchar(sub_node->GetText(), zigbee_device.version, sizeof(zigbee_device.version) / sizeof(TCHAR));
            }
        }
        
        slice_info.devices.push_back(zigbee_device);
    }

    AK_LOG_INFO << "Parsed slice info - trace_id:" << slice_info.trace_id 
                << " current_slice:" << slice_info.current_slice 
                << " total_slice:" << slice_info.total_slice
                << " devices_count:" << slice_info.devices.size();

    return 0;
}

}

#endif 