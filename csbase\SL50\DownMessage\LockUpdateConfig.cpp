#include "LockUpdateConfig.h"

LockUpdateConfig::LockUpdateConfig()
    : ServiceCall(DEFAULT_SERVICE_TYPE, DEFAULT_SERVICE_DOMAIN, DEFAULT_SERVICE_NAME) {}

LockUpdateConfig::LockUpdateConfig(const ServiceCall& sc)
    : ServiceCall(sc) {}

std::string LockUpdateConfig::to_json() {
    Json::Value param;
    Json::Value j;
    BaseParam::to_json(j, COMMOND);

    ServiceCall::to_json(param);
    Json::Value sd;
    sd["entity_id"] = entity_id_;
    sd["language"] = language_;
    sd["volume"] = volume_;
    sd["dwell_alarm"] = dwell_alarm_;
    sd["dwell_time"] = dwell_time_;
    sd["monitoring_scope"] = monitoring_scope_;
    sd["double_verification"] = double_verification_;
    param["service_data"] = sd;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}

std::string LockUpdateConfig::to_json(LockUpdateConfigType config_type) {
    Json::Value param;
    Json::Value j;
    BaseParam::to_json(j, COMMOND);

    ServiceCall::to_json(param);
    Json::Value sd;

    switch (config_type)
    {
        case LockUpdateConfigType::LANGUAGE:
        {
            sd["language"] = language_;
            break;
        }
        case LockUpdateConfigType::VOLUME:
        {
            sd["volume"] = volume_;
            break;
        }
        case LockUpdateConfigType::MONITORING_SCOPE:
        case LockUpdateConfigType::DWELL_ALARM:
        {
            sd["monitoring_scope"] = monitoring_scope_;
            sd["dwell_alarm"] = dwell_alarm_;
            sd["dwell_time"] = dwell_time_;
            break;
        }
        case LockUpdateConfigType::DWELL_TIME:
        {
            sd["dwell_time"] = dwell_time_;
            break;
        }
        case LockUpdateConfigType::DOUBLE_VERIFICATION:
        {
            sd["double_verification"] = double_verification_;
            break;
        }
        case LockUpdateConfigType::ALL:
        {
            return to_json();
        }
    }

    param["service_data"] = sd;
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}

void LockUpdateConfig::from_json(const std::string& json_str) {
    Json::Value j;
    Json::Reader reader;
    if (!reader.parse(json_str, j)) {
        throw std::runtime_error("Failed to parse JSON string");
    }
    
    if (j.isMember("id")) id_ = j["id"].asString();
    if (j.isMember("command")) command_ = j["command"].asString();
    ServiceCall::from_json(j);
    if (j.isMember("service_data")) {
        Json::Value sd = j["service_data"];
        if (sd.isMember("entity_id")) entity_id_ = sd["entity_id"].asString();
        if (sd.isMember("language")) language_ = sd["language"].asString();
        if (sd.isMember("volume")) volume_ = sd["volume"].asString();
        if (sd.isMember("dwell_alarm")) dwell_alarm_ = sd["dwell_alarm"].asBool();
        if (sd.isMember("dwell_time")) dwell_time_ = sd["dwell_time"].asInt();
        if (sd.isMember("monitoring_scope")) monitoring_scope_ = sd["monitoring_scope"].asString();
        if (sd.isMember("double_verification")) double_verification_ = sd["double_verification"].asBool();
    }
}