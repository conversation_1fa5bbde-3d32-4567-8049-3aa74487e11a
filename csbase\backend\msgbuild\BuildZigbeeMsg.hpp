#ifndef __BUILD_ZIGBEE_MSG_HPP__
#define __BUILD_ZIGBEE_MSG_HPP__

#include "util.h"
#include "XmlMsgBuilder.h"
#include "AkLogging.h"
#include "AK.Server.pb.h"
#include <string>

namespace akcs_msgbuild {

/*
构建请求Zigbee设备详细信息的消息
<Msg>
<Type>RequestZigbeeDetails</Type>
<Params>
    <ZigbeeDeviceID>1</ZigbeeDeviceID>
    <ZigbeeDeviceID>3</ZigbeeDeviceID>
</Params>
</Msg>
如果为空的话也会下发：
<Msg>
<Type>RequestZigbeeDetails</Type>
<Params>
</Params>
</Msg>
*/
inline int BuildRequestZigbeeDetailsMsg(const AkcsStringList& zigbee_device_ids, std::string &msg)
{
    if (zigbee_device_ids.empty())
    {
        // 当设备列表为空时，手动构建空的ACK消息
        msg = "<Msg>\n<Type>RequestZigbeeDetails</Type>\n<Params>\n</Params>\n</Msg>";
    }
    else
    {
        XmlBuilder xml_builder("RequestZigbeeDetails");
        xml_builder.AddMultiKeyValue("ZigbeeDeviceID", zigbee_device_ids);
        msg = xml_builder.GenerateXML();
    }
    
    AK_LOG_INFO << "BuildRequestZigbeeDetailsMsg msg:\n" << msg;
    
    return 0;
}

/*
构建控制Zigbee设备的消息
<Msg>
<Type>ControlZigbeeDevice</Type>
<Params>
  <TraceID>0123233000</TraceID>
  <ZigbeeDeviceID>1</ZigbeeDeviceID>
  <DeviceType>0</DeviceType>
  <Switch>0</Switch>
  <TargetTemperature>26</TargetTemperature>
  <HVACMode>0</HVACMode>
 </Params>
</Msg>
*/
inline int BuildControlZigbeeDeviceMsg(const std::string& zigbee_device_id, int device_type, int switch_status, const std::string& temperature, int hvac_mode, std::string traceid, std::string &msg)
{
    XmlBuilder xml_builder("ControlZigbeeDevice");
    
    XmlKV tag_map;
    tag_map["TraceID"] = traceid;
    tag_map["ZigbeeDeviceID"] = zigbee_device_id;
    tag_map["DeviceType"] = std::to_string(device_type);
    tag_map["Switch"] = std::to_string(switch_status);
    
    // 不管什么设备类型都下发TargetTemperature和HVACMode字段，设备那边按需读取
    tag_map["TargetTemperature"] = temperature;
    tag_map["HVACMode"] = std::to_string(hvac_mode);
    
    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildControlZigbeeDeviceMsg msg:\n" << msg;
    
    return 0;
}

/*
构建Zigbee设备状态变化通知消息给App
<Msg>
<Type>RequestAppChangeZigbeeDevice</Type>
<Params>
  <MAC>0C11050068F4</MAC>
  <ZigbeeDeviceID>1</ZigbeeDeviceID>
  <DeviceType>0</DeviceType>
  <Switch>0</Switch>
  <Temperature>24.8</Temperature>
  <TargetTemperature>26</TargetTemperature>
  <HVACMode>0</HVACMode>
  <TraceID>0123233000</TraceID>
</Params>
</Msg>
*/
inline void BuildZigbeeStatusChangeMsg(const AK::Server::P2PSendZigbeeStatusChangeMsg& p2p_msg, std::string& msg)
{
    XmlBuilder xml_builder("RequestAppChangeZigbeeDevice");
    
    XmlKV tag_map;
    tag_map["MAC"] = p2p_msg.mac();
    tag_map["ZigbeeDeviceID"] = p2p_msg.zigbee_device_id();
    tag_map["DeviceType"] = std::to_string(p2p_msg.device_type());
    tag_map["Switch"] = std::to_string(p2p_msg.switch_status());
    tag_map["Temperature"] = p2p_msg.temperature();
    tag_map["TargetTemperature"] = p2p_msg.target_temperature();
    tag_map["HVACMode"] = std::to_string(p2p_msg.hvac_mode());
    tag_map["TraceID"] = p2p_msg.trace_id();
    
    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();
    
    AK_LOG_INFO << "BuildZigbeeStatusChangeMsg msg:\n" << msg;
}

} // namespace CMsgBuildHandle

#endif // __BUILD_ZIGBEE_MSG_HPP__ 